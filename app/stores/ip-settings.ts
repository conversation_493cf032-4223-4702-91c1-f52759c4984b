import { orderBy } from 'lodash'

export interface IpSetting {
  ip_address: string
  status: boolean
  priority: number
  id: string
  tenant_id: string
  created_username: string
  updated_username: string
  created_at: string
  updated_at: string
}

export interface IpSettingsResponse {
  settings: IpSetting[]
  pagination: string
}

export interface CreateIpSettingPayload {
  ip_address: string
  status: boolean
  priority: number
}

export interface UpdateIpSettingPayload {
  ip_address: string
  status: boolean
  priority: number
}

export interface IpSettingsFilter {
  ip_address?: string
  priority_min?: number
  priority_max?: number
  status?: boolean
}

export const useIpSettingsStore = defineStore('ipSettingsStore', {
  persist: {
    pick: [],
    storage: window?.localStorage
  },
  state: () => ({
    ipSettings: [] as IpSetting[],
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    ipSettingsPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    ipSettingsTotal: 0,
    ipSettingsFilter: {} as IpSettingsFilter,
    selectedIpSettings: [] as IpSetting[]
  }),
  getters: {
    filteredIpSettings: (state) => {
      let filtered = [...state.ipSettings]

      // Filter by IP address (exact match)
      if (state.ipSettingsFilter.ip_address) {
        filtered = filtered.filter(setting =>
          setting.ip_address === state.ipSettingsFilter.ip_address
        )
      }

      // Filter by priority range
      if (state.ipSettingsFilter.priority_min !== undefined) {
        filtered = filtered.filter(setting =>
          setting.priority >= state.ipSettingsFilter.priority_min!
        )
      }

      if (state.ipSettingsFilter.priority_max !== undefined) {
        filtered = filtered.filter(setting =>
          setting.priority <= state.ipSettingsFilter.priority_max!
        )
      }

      // Filter by status
      if (state.ipSettingsFilter.status !== undefined) {
        filtered = filtered.filter(setting =>
          setting.status === state.ipSettingsFilter.status
        )
      }

      return filtered
    }
  },
  actions: {
    resetFilters() {
      this.ipSettingsFilter = {}
    },

    async fetchIpSettings(tenant_id: string, force = false) {
      // Check if IP settings are already fetched and tenant_id is same
      if (this.ipSettings.length > 0 && !force) {
        if (this.ipSettings.every(setting => setting.tenant_id === tenant_id)) {
          return this.ipSettings
        }
      }

      try {
        this.loadings.fetchIpSettings = true
        this.errors.fetchIpSettings = null
        this.ipSettings = []

        const { custom } = useRoleBasedApiCalls()

        const params: any = {
          page: this.ipSettingsPagination.page,
          page_size: this.ipSettingsPagination.pageCount,
          order: this.ipSettingsPagination.asc
        }

        // Add filter parameters
        if (this.ipSettingsFilter.ip_address) {
          params.ip_address = this.ipSettingsFilter.ip_address
        }
        if (this.ipSettingsFilter.priority_min !== undefined) {
          params.priority_min = this.ipSettingsFilter.priority_min
        }
        if (this.ipSettingsFilter.priority_max !== undefined) {
          params.priority_max = this.ipSettingsFilter.priority_max
        }
        if (this.ipSettingsFilter.status !== undefined) {
          params.status = this.ipSettingsFilter.status
        }

        const response = await custom<IpSettingsResponse>({
          operator: `/v2/ipSettings/all/tenants/{tenantId}`,
          admin: `/v2/ipSettings/all/tenants/{tenantId}`,
          staff: `/v2/ipSettings/all/tenants/{tenantId}`,
          default: `/v2/ipSettings/all/tenants/{tenantId}`,
          params: {
            tenantId: tenant_id
          },
          query: params
        }, 'get')

        this.ipSettings = orderBy(response.settings || [], ['updated_at'], ['desc'])
        const pagination = tryParseJson(response?.pagination)
        this.ipSettingsTotal = pagination?.total_count || 0
        return response.settings
      } catch (error: any) {
        this.errors.fetchIpSettings = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchIpSettings = false
      }
    },

    async createIpSetting(tenant_id: string, payload: CreateIpSettingPayload) {
      try {
        this.loadings.createIpSetting = true
        this.errors = {}

        const { custom } = useRoleBasedApiCalls()

        const response = await custom<IpSetting>({
          operator: `/v2/ipSettings/tenants/{tenantId}`,
          admin: `/v2/ipSettings/tenants/{tenantId}`,
          staff: `/v2/ipSettings/tenants/{tenantId}`,
          default: `/v2/ipSettings/tenants/{tenantId}`,
          params: {
            tenantId: tenant_id
          }
        }, 'post', payload)

        this.ipSettings.unshift(response)
        this.ipSettingsTotal += 1

        return response
      } catch (error: any) {
        this.errors.createIpSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.createIpSetting = false
      }
    },

    async updateIpSetting(id: string, tenant_id: string, payload: UpdateIpSettingPayload) {
      console.log('🚀 ~ updateIpSetting ~ payload:', payload)
      try {
        this.loadings.updateIpSetting = true
        this.errors = {}

        const { custom } = useRoleBasedApiCalls()

        const response = await custom<IpSetting>({
          operator: `/v2/ipSettings/{id}/tenants/{tenantId}`,
          admin: `/v2/ipSettings/{id}/tenants/{tenantId}`,
          staff: `/v2/ipSettings/{id}/tenants/{tenantId}`,
          default: `/v2/ipSettings/{id}/tenants/{tenantId}`,
          params: {
            tenantId: tenant_id,
            id: id
          }
        }, 'patch', payload)

        this.ipSettings = this.ipSettings.map((setting) => {
          if (setting.id === response.id) {
            return response
          }
          return setting
        })

        return response
      } catch (error: any) {
        this.errors.updateIpSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateIpSetting = false
      }
    },

    async deleteIpSetting(id: string, tenant_id: string) {
      try {
        this.loadings.deleteIpSetting = true
        this.errors.deleteIpSetting = null

        const { custom } = useRoleBasedApiCalls()

        await custom({
          operator: `/v2/ipSettings/{id}/tenants/{tenantId}`,
          admin: `/v2/ipSettings/{id}/tenants/{tenantId}`,
          staff: `/v2/ipSettings/{id}/tenants/{tenantId}`,
          default: `/v2/ipSettings/{id}/tenants/{tenantId}`,
          params: {
            tenantId: tenant_id,
            id: id
          }
        }, 'delete')

        this.ipSettings = this.ipSettings.filter(setting => setting.id !== id)
        this.ipSettingsTotal -= 1

        return true
      } catch (error: any) {
        this.errors.deleteIpSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteIpSetting = false
      }
    }
  }
})
