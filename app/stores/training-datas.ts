import { saveAs } from 'file-saver'
import { mkConfig, generateCsv, asString } from 'export-to-csv'
import { cloneDeep } from 'lodash'
import type { RagKnowledgeDocument } from '~/types/knowledge'

export const useTrainingDatasStore = defineStore('trainingDatasStore', {
  // persist: {
  //   pick: ['selectedTenantId'],
  //   storage: window?.localStorage
  // },
  state: () => ({
    // .csv,.xlsx,.docx,.pptx,.txt,.pdf,.md,.json
    trainingAcceptsFiles: [
      '.csv',
      '.xlsx',
      '.docx',
      '.pptx',
      '.txt',
      '.pdf',
      '.md',
      '.json',
      'application/json',
      'text/csv',
      'text/markdown',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/pdf'
    ],
    trainingDataFilter: {} as Record<string, any>,
    trainingDatasPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    trainingDatasTotal: 0,
    trainingDatas: [] as any[],
    selectedKnowledgeDocument: null as RagKnowledgeDocument | null,
    loadings: {
      deleteKnowledgeContent: {} as Record<string, any>,
      deleteTrainingData: {} as Record<string, any>,
      updateTrainingData: {} as Record<string, any>,
      initKnowledgeApi: false
    } as Record<string, any>,
    errors: {
      deleteKnowledgeContent: {} as Record<string, any>,
      deleteTrainingData: {} as Record<string, any>,
      updateTrainingData: {} as Record<string, any>,
      initKnowledgeApi: null
    } as Record<string, any>,
    trainingDataNew: {
      documents: [
        {
          name: '',
          file: null,
          priority: 100,
          label: '',
          labels: [],
          preview: null,
          loadings: {} as Record<string, any>,
          errors: {} as Record<string, any>,
          expand: false,
          success: null,
          // FAQ-related fields
          as_faq: false,
          faq_header_row: [0],
          faq_id_header: null,
          faq_subtitle_header: null,
          faq_question_header: null,
          faq_answer_header: null,
          faq_category_header: null
        }
      ] as any[],
      websites: [
        {
          name: '',
          url: '',
          priority: 100,
          label: '',
          labels: [],
          dynamic: false,
          ignore_tags: [] as string[],
          ignore_classes: [] as string[],
          preview: null,
          loadings: {} as Record<string, any>,
          errors: {} as Record<string, any>,
          expand: false,
          success: null
        }
      ]
    },
    trainingDataDetail: [] as any[],
    trainingDataDetailFilter: {} as Record<string, any>,
    trainingDataDetailPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    trainingDataDetailsTotal: 0,

    trainingDataDetailContent: null as any,

    hasAnyUnreflected: false,

    seletedKnowledges: [] as any[],

    searchContents: [] as any[],

    // Demo data for tours
    originalDatasourceData: null as any
  }),
  getters: {
    trainingDatasCount(): number {
      return this.trainingDatasTotal
    },
    trainingDatasNavigators(): any[] {
      const { selectedTenantId, selectedEnvId } = useApp()
      return this.trainingDatas.map((trainingData: any) => ({
        ...trainingData,
        label: trainingData.name,
        to: `/${selectedTenantId.value}/${selectedEnvId.value}/training-data/${trainingData.id}`
      }))
    },

    trainingDatasNavigatorsTopFive(): any[] {
      const { selectedTenantId, selectedEnvId } = useApp()
      const topFive = cloneDeep(this.trainingDatas)
      if (topFive.length > 5) {
        topFive.length = 5
      }
      return topFive.map((trainingData: any, index: number) => ({
        ...trainingData,
        type: 'training-data-top-five-' + index,
        label: trainingData.name,
        to: `/${selectedTenantId.value}/${selectedEnvId.value}/training-data/${trainingData.id}`
      }))
    },

    knowledgesCount(): number {
      return this.trainingDataDetail?.length
    },
    trainingDataNewSummary(): any[] {
      const route = useRoute()
      const totalFiles = this.trainingDataNew.documents.filter(
        obj => obj.file
      ).length
      const totalFilesSize = this.trainingDataNew.documents.reduce(
        (acc, obj) => {
          return acc + (obj.file ? obj.file.size : 0)
        },
        0
      )

      const totalWebsites = this.trainingDataNew.websites.filter(
        obj => obj.url
      ).length
      return [
        {
          label: `${totalFiles} ファイル (${bytesToSize(totalFilesSize)})`,
          icon: 'mdi:file-document',
          to: `/${route.params.tenantId}/training-data/new/`,
          exact: true
        },
        {
          label: `${totalWebsites} Website`,
          icon: 'mdi:web',
          to: `/${route.params.tenantId}/training-data/new/web`
        }
      ]
    },

    selectedTrainingData(): any {
      const route = useRoute()
      return this.trainingDatas.find(
        trainingData => trainingData.id == route.params.id
      )
    },

    trainingDataDetailRows(): any[] {
      const { page, pageCount } = this.trainingDataDetailPagination
      return this.trainingDataDetail.slice(
        (page - 1) * pageCount,
        page * pageCount
      )
    },

    hasTrainingDataDetailsUnreflected(): boolean {
      return this.trainingDataDetail?.some((content: any) =>
        [0].includes(content.status)
      )
    }
  },
  actions: {
    async fetchTrainingDatas(
      tenant_id: string,
      env_id: string,
      searchQuery?: string
    ) {
      try {
        this.loadings.fetchTrainingDatas = true
        this.errors.fetchTrainingDatas = null
        this.trainingDatas = []
        this.trainingDatasTotal = 0

        // Check if tenant_id is undefined or 'undefined'
        if (!tenant_id || tenant_id === 'undefined') {
          console.warn('fetchTrainingDatas called with undefined tenant_id')
          return false
        }

        // Check if env_id is undefined or 'undefined'
        if (!env_id || env_id === 'undefined') {
          console.warn('fetchTrainingDatas called with undefined env_id')
          return false
        }

        const params = {
          page: this.trainingDatasPagination.page,
          page_size: this.trainingDatasPagination.pageCount,
          order: this.trainingDatasPagination.asc
        } as Record<string, any>

        // Add search query parameter if provided
        if (searchQuery) {
          params.name = searchQuery
        }

        if (this.trainingDataFilter?.status?.length === 1) {
          params.enabled = this.trainingDataFilter?.status?.some(
            (status: any) => status?.value === 'enabled'
          )
        }

        if (this.trainingDataFilter?.contextTypes?.length) {
          params.original_context_type
            = this.trainingDataFilter?.contextTypes?.map(
              (contextType: any) => contextType?.value
            )
        }
        if (this.trainingDataFilter?.source_url) {
          params.source_url = this.trainingDataFilter.source_url
        }
        const response = await useAPI().adminService.get(
          `/v2/knowledge/documents/all/tenants/${tenant_id}/env/${env_id}`,
          {
            params,
            paramsSerializer: {
              indexes: null
            }
          }
        )
        this.trainingDatas = response.data.documents
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.trainingDatasTotal = pagination?.total_count
        return response.data
      } catch (error: any) {
        this.errors.fetchTrainingDatas = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchTrainingDatas = false
      }
    },
    async fetchTrainingDataDetail(
      document_id: string,
      tenant_id: string,
      env_id: string,
      searchQuery?: string
    ) {
      try {
        this.loadings.fetchTrainingDataDetail = true
        this.errors.fetchTrainingDataDetail = null
        if (
          this.seletedKnowledges?.some(
            (knowledge: any) => knowledge.document_id !== document_id
          )
        ) {
          this.seletedKnowledges = []
        }
        this.trainingDataDetail = []

        this.trainingDataDetailsTotal = 0

        const params = {
          page: this.trainingDataDetailPagination.page,
          page_size: this.trainingDataDetailPagination.pageCount,
          order: this.trainingDataDetailPagination.asc
        } as Record<string, any>

        // Add search query parameter if provided
        if (searchQuery) {
          params.q = searchQuery
        }

        if (this.trainingDataDetailFilter?.enabled?.length === 1) {
          params.enabled = this.trainingDataDetailFilter?.enabled?.some(
            (enabled: any) => enabled?.value === 'enabled'
          )
        }

        if (this.trainingDataDetailFilter?.label?.length) {
          params.labels = this.trainingDataDetailFilter.label.map(
            (label: string) => label
          )
        }
        // comment out when filter by status, priority number
        // if (this.trainingDataDetailFilter?.status) {
        //   params.status = this.trainingDataDetailFilter.status.value
        // }

        // if (this.trainingDataDetailFilter?.priorityMax) {
        //   params.priority_max = this.trainingDataDetailFilter.priorityMax
        // }

        // if (this.trainingDataDetailFilter?.priorityMin) {
        //   params.priority_min = this.trainingDataDetailFilter.priorityMin
        // }
        const response = await useAPI().adminService.get(
          `/v2/knowledge/documents/${document_id}/contents/detail/tenants/${tenant_id}/env/${env_id}`,
          {
            params,
            paramsSerializer: {
              indexes: null
            }
          }
        )

        const pagination = tryParseJson(response?.headers['x-pagination'])

        this.trainingDataDetail = response.data.contents
        this.trainingDataDetailsTotal
          = pagination?.total_pages * this.trainingDataDetailPagination.pageCount
        return response.data
      } catch (error: any) {
        this.errors.fetchTrainingDataDetail = error?.response?.data || error
        // check if error_code: 2103, navigate to training data list
        if (error?.response?.data?.error_code === 2103) {
          const router = useRouter()
          router.push(`/${tenant_id}/${env_id}/training-data/`)
        }
        return false
      } finally {
        this.loadings.fetchTrainingDataDetail = false
      }
    },

    async deleteTrainingData(
      document_id: string,
      tenant_id: string,
      env_id: string
    ) {
      try {
        this.loadings.updateTrainingData[document_id] = true
        this.errors.updateTrainingData[document_id] = null
        await useAPI().adminService.delete(
          `/v2/knowledge/documents/${document_id}/tenants/${tenant_id}/env/${env_id}`
        )
        this.trainingDatas = this.trainingDatas.filter(
          trainingData => trainingData.id !== document_id
        )
        return true
      } catch (error: any) {
        this.errors.updateTrainingData[document_id]
          = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateTrainingData[document_id] = false
        this.checkIfHasAnyUnreflected(tenant_id, env_id)
      }
    },

    async updateTrainingData(
      document_id: string,
      tenant_id: string,
      env_id: string,
      payload: {
        enabled?: boolean
        priority?: number
        labels?: string[]
      }
    ) {
      try {
        this.loadings.updateTrainingData[document_id] = true
        this.errors.updateTrainingData[document_id] = null
        const res = await useAPI().adminService.patch(
          `/v2/knowledge/documents/${document_id}/tenants/${tenant_id}/env/${env_id}`,
          payload
        )
        this.trainingDatas = this.trainingDatas.map((trainingData) => {
          if (trainingData.id === document_id) {
            return res.data
          }
          return trainingData
        })
        return true
      } catch (error: any) {
        this.errors.updateTrainingData[document_id]
          = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateTrainingData[document_id] = false
        this.checkIfHasAnyUnreflected(tenant_id, env_id)
      }
    },

    async fetchTrainingDataDetailContent(
      document_id: string,
      content_id: string,
      tenant_id: string,
      env_id: string
    ) {
      try {
        this.loadings.fetchTrainingDataDetailContent = true
        this.errors.fetchTrainingDataDetailContent = null
        this.trainingDataDetailContent = null
        const response = await useAPI().adminService.get(
          `/v2/knowledge/documents/${document_id}/contents/${content_id}/tenants/${tenant_id}/env/${env_id}`
        )
        this.trainingDataDetailContent = response.data
        return response.data
      } catch (error: any) {
        this.errors.fetchTrainingDataDetailContent
          = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchTrainingDataDetailContent = false
      }
    },

    async previewTrainingData(tenant_id: string, env_id: string) {
      try {
        this.loadings.previewTrainingData = true
        this.errors.previewTrainingData = null
        // for each document, upload to server
        for (const document of this.trainingDataNew.documents) {
          try {
            document.loadings['preview'] = true
            if (!document.file || !document.name) {
              continue
            }
            const formData = new FormData()
            formData.append('file', document.file)
            formData.append('name', document.name)

            // Add FAQ-related parameters if FAQ mode is enabled
            if (document.as_faq) {
              formData.append('as_faq', 'true')
              if (
                document.faq_header_row
                && document.faq_header_row.length > 0
              ) {
                document.faq_header_row.forEach((row: number) => {
                  formData.append('faq_header_row[]', row.toString())
                })
              }
              if (document.faq_id_header) {
                formData.append('faq_id_header', document.faq_id_header)
              }
              if (document.faq_subtitle_header) {
                formData.append(
                  'faq_subtitle_header',
                  document.faq_subtitle_header
                )
              }
              if (document.faq_question_header) {
                formData.append(
                  'faq_question_header',
                  document.faq_question_header
                )
              }
              if (document.faq_answer_header) {
                formData.append(
                  'faq_answer_header',
                  document.faq_answer_header
                )
              }
              if (document.faq_category_header) {
                formData.append(
                  'faq_category_header',
                  document.faq_category_header
                )
              }
            }

            const response = await useAPI().adminService.post(
              `/v2/knowledge/upload/preview/tenants/${tenant_id}/env/${env_id}`,
              formData,
              {
                headers: {
                  'Content-Type': 'multipart/form-data'
                }
              }
            )
            document.preview = response.data?.result
            document.loadings['preview'] = false
          } catch (error: any) {
            console.log('🚀 ~ previewTrainingData ~ error:', error)
            document.errors['preview'] = error?.response?.data || error
          } finally {
            document.loadings['preview'] = false
          }
        }

        // for each document, upload to server
        for (const website of this.trainingDataNew.websites) {
          try {
            website.loadings['preview'] = true
            if (!website.url || !website.name) {
              continue
            }
            const response = await useAPI().adminService.post(
              `/v2/knowledge/import/webpage/preview/tenants/${tenant_id}/env/${env_id}`,
              {
                name: website.name,
                url: website.url,
                dynamic: website.dynamic,
                ignore_tags: website.ignore_tags,
                ignore_classes: website.ignore_classes
              }
            )
            // ウェブページインポート・プレビューする際に、Robots.txtを取得し可否を調べます。不可の場合は何もせず、レスポンスの`robots_disallowed`がTrueと返却されます。
            if (response.data?.robots_disallowed) {
              website.errors['preview']
                = 'Robots.txtの設定により、このページはインポートできません。'
            } else {
              website.preview = response.data?.result
              website.loadings['preview'] = false
            }
          } catch (error: any) {
            console.log('🚀 ~ previewTrainingData ~ error:', error)
            website.errors['preview'] = error?.response?.data || error
          } finally {
            website.loadings['preview'] = false
          }
        }
        return true
      } catch (error: any) {
        this.errors.previewTrainingData = error?.response?.data || error
        return false
      } finally {
        this.loadings.previewTrainingData = false
      }
    },

    async trainingData(tenant_id: string, env_id: string) {
      try {
        this.loadings.trainingData = true
        this.errors.trainingData = null
        const reimport = this.selectedKnowledgeDocument != null
        let allDone = true
        // for each document, upload to server
        for (const document of this.trainingDataNew.documents) {
          try {
            document.errors['training'] = null
            document.loadings['training'] = true
            if (!document.file || !document.name) {
              console.log('🚀 ~ trainingData ~ document:', document)
              continue
            }
            const formData = new FormData()
            formData.append('file', document.file)
            formData.append('name', document.name)
            formData.append('priority', document.priority || 100)
            formData.append('labels', document.labels)

            // Add FAQ-related parameters if FAQ mode is enabled
            if (document.as_faq) {
              formData.append('as_faq', 'true')
              if (
                document.faq_header_row
                && document.faq_header_row.length > 0
              ) {
                document.faq_header_row.forEach((row: number) => {
                  formData.append('faq_header_row[]', row.toString())
                })
              }
              if (document.faq_id_header) {
                formData.append('faq_id_header', document.faq_id_header)
              }
              if (document.faq_subtitle_header) {
                formData.append(
                  'faq_subtitle_header',
                  document.faq_subtitle_header
                )
              }
              if (document.faq_question_header) {
                formData.append(
                  'faq_question_header',
                  document.faq_question_header
                )
              }
              if (document.faq_answer_header) {
                formData.append(
                  'faq_answer_header',
                  document.faq_answer_header
                )
              }
              if (document.faq_category_header) {
                formData.append(
                  'faq_category_header',
                  document.faq_category_header
                )
              }
            }
            const response = await (reimport
              ? useAPI().adminService.put(
                  `/v2/knowledge/documents/${this.selectedKnowledgeDocument?.id}/reupload/tenants/${tenant_id}/env/${env_id}`,
                  formData,
                  {
                    headers: {
                      'Content-Type': 'multipart/form-data'
                    }
                  }
                )
              : useAPI().adminService.post(
                  `/v2/knowledge/upload/tenants/${tenant_id}/env/${env_id}`,
                  formData,
                  {
                    headers: {
                      'Content-Type': 'multipart/form-data'
                    }
                  }
                ))
            document.success = response.data?.success || false
            allDone = allDone && document.success
            if (!document.success) {
              document.errors['training'] = {
                error_message:
                  '登録に失敗しました。データが正しいか確認してください。'
              }
            }
          } catch (error: any) {
            console.log('🚀 ~ trainingData ~ error:', error)
            document.errors['training'] = error?.response?.data || error
            document.success = false
            allDone = false
          } finally {
            document.loadings['training'] = false
          }
        }

        // for each document, upload to server
        for (const website of this.trainingDataNew.websites) {
          try {
            website.errors['training'] = null
            website.loadings['training'] = true
            if (!website.url || !website.name) {
              console.log('🚀 ~ trainingData ~ website:', website)
              continue
            }
            const response = await (reimport
              ? useAPI().adminService.put(
                  `/v2/knowledge/documents/${this.selectedKnowledgeDocument?.id}/reimport/webpage/tenants/${tenant_id}/env/${env_id}`,
                  {
                    name: website.name,
                    url: website.url,
                    enabled: true,
                    priority: website.priority || 100,
                    labels: website.labels,
                    dynamic: website.dynamic,
                    ignore_tags: website.ignore_tags,
                    ignore_classes: website.ignore_classes
                  }
                )
              : useAPI().adminService.post(
                  `/v2/knowledge/import/webpage/tenants/${tenant_id}/env/${env_id}`,
                  {
                    name: website.name,
                    url: website.url,
                    enabled: true,
                    priority: website.priority || 100,
                    labels: website.labels,
                    dynamic: website.dynamic,
                    ignore_tags: website.ignore_tags,
                    ignore_classes: website.ignore_classes
                  }
                ))
            website.success = response.data?.success || false
            allDone = allDone && website.success

            if (!website.success) {
              if (response.data?.robots_disallowed) {
                website.errors['training'] = {
                  error_message:
                    'Robots.txtの設定により、このページはインポートできません。'
                }
              } else {
                website.errors['training'] = {
                  error_message:
                    '登録に失敗しました。データが正しいか確認してください。'
                }
              }
            }
          } catch (error: any) {
            console.log('🚀 ~ trainingData ~ error:', error)
            website.errors['training'] = error?.response?.data || error
            website.success = false
            allDone = false
          } finally {
            website.loadings['training'] = false
          }
        }

        return allDone
      } catch (error: any) {
        console.log('🚀 ~ trainingData ~ error:', error)
        this.errors.trainingData = error?.response?.data || error
        return false
      } finally {
        this.loadings.trainingData = false
      }
    },

    addDocument(
      file: null | File = null,
      name: string = '',
      priority: number = 100,
      labels: string[] = []
    ) {
      console.log('🚀 ~ file:', file)
      this.trainingDataNew.documents.push({
        name: name,
        file: file || null,
        priority: priority,
        label: '',
        labels: labels,
        preview: null,
        loadings: {} as Record<string, any>,
        errors: {} as Record<string, any>,
        expand: false,
        success: null,
        // FAQ-related fields
        as_faq: false,
        faq_header_row: [0],
        faq_id_header: null,
        faq_subtitle_header: null,
        faq_question_header: null,
        faq_answer_header: null,
        faq_category_header: null
      })
      // scroll to bottom
      setTimeout(() => {
        nextTick(() => {
          const container = document.getElementById('training-data-new')
          // scroll smoothly to bottom
          container?.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
          })
        })
      }, 100)
    },

    removeDocument(index: number) {
      this.trainingDataNew.documents.splice(index, 1)
    },

    addWebsite() {
      this.trainingDataNew.websites.push({
        name: '',
        url: '',
        priority: 100,
        label: '',
        labels: [],
        preview: null,
        dynamic: false,
        loadings: {} as Record<string, any>,
        errors: {} as Record<string, any>,
        expand: false,
        success: null,
        ignore_tags: [] as string[],
        ignore_classes: [] as string[]
      })
      // scroll to bottom
      setTimeout(() => {
        nextTick(() => {
          const container = document.getElementById('training-data-new')
          // scroll smoothly to bottom
          container?.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
          })
        })
      }, 100)
    },

    removeWebsite(index: number) {
      this.trainingDataNew.websites.splice(index, 1)
    },

    async checkIfHasAnyUnreflected(tenant_id: string, env_id: string) {
      try {
        this.loadings.checkIfHasAnyUnreflected = true
        this.errors.checkIfHasAnyUnreflected = null
        this.hasAnyUnreflected = false

        // Check if tenant_id is undefined or 'undefined'
        if (!tenant_id || tenant_id === 'undefined') {
          console.warn(
            'checkIfHasAnyUnreflected called with undefined tenant_id'
          )
          return false
        }

        // Check if env_id is undefined or 'undefined'
        if (!env_id || env_id === 'undefined') {
          console.warn('checkIfHasAnyUnreflected called with undefined env_id')
          return false
        }

        const response = await useAPI().adminService.get(
          `/v2/knowledge/documents/unreflected/tenants/${tenant_id}/env/${env_id}`
        )
        this.hasAnyUnreflected = response.data.result
        return response.data
      } catch (error: any) {
        this.errors.checkIfHasAnyUnreflected = error?.response?.data || error
        return false
      } finally {
        this.loadings.checkIfHasAnyUnreflected = false
      }
    },

    async updateTrainingDataDetail(
      document_id: string,
      content_id: string,
      tenant_id: string,
      env_id: string
    ) {
      try {
        this.loadings.updateTrainingDataDetail = true
        this.errors.updateTrainingDataDetail = null
        const response = await useAPI().adminService.put(
          `/v2/knowledge/documents/${document_id}/contents/${content_id}/tenants/${tenant_id}/env/${env_id}`,
          {
            enabled: this.trainingDataDetailContent?.enabled,
            priority: +this.trainingDataDetailContent?.priority,
            labels: this.trainingDataDetailContent?.label,
            content: this.trainingDataDetailContent?.content
          }
        )
        this.trainingDataDetailContent = {
          ...this.trainingDataDetailContent,
          ...response.data
        }
        return response.data
      } catch (error: any) {
        this.errors.updateTrainingDataDetail = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateTrainingDataDetail = false
        this.checkIfHasAnyUnreflected(tenant_id, env_id)
      }
    },

    exportKnowledgesToCSV(rows: any[], documentName?: string) {
      rows = rows.map(row => ({
        ...row,
        label: row?.label?.join(';')
      }))
      const config = mkConfig({ useKeysAsHeaders: true })
      const csvOutput = generateCsv(config)(rows)
      const blob = new Blob([csvOutput], {
        type: 'text/csv;charset=utf-8'
      })
      saveAs(blob, `${documentName || 'knowledges'}.csv`)
    },

    async bulkUpdateKnowledges(
      targets: any[],
      bulkUpdates: {
        priority?: number
        labels?: string[]
        enabled?: boolean
      }
    ) {
      try {
        this.loadings.bulkUpdateKnowledges = true
        this.errors.bulkUpdateKnowledges = null
        await Promise.allSettled(
          targets.map((target) => {
            const { document_id, id: content_id, tenant_id, env_id } = target
            return useAPI().adminService.patch(
              `/v2/knowledge/documents/${document_id}/contents/${content_id}/tenants/${tenant_id}/env/${env_id}`,
              {
                // priority: target.priority,
                // labels: target.labels,
                // enabled: target.enabled,
                // content: target.content,
                ...bulkUpdates
              }
            )
          })
        )
        // this.trainingDataDetail = this.trainingDataDetail.map((content) => {
        //   const target = targets.find(target => target.id === content.id)
        //   if (target) {
        //     return {
        //       ...content,
        //       ...target,
        //       ...bulkUpdates
        //     }
        //   }
        //   return content
        // })
        return true
      } catch (error: any) {
        this.errors.bulkUpdateKnowledges = error?.response?.data || error
        return false
      } finally {
        this.loadings.bulkUpdateKnowledges = false
        const { tenant_id, env_id } = targets[0]
        this.checkIfHasAnyUnreflected(tenant_id, env_id)
      }
    },

    async deleteKnowledgeContent(targets: any[]) {
      console.log('🚀 ~ deleteKnowledgeContent ~ targets:', targets)
      targets.map(async (target) => {
        const { document_id, id: content_id, tenant_id, env_id } = target
        try {
          this.loadings.deleteKnowledgeContent[content_id] = true
          this.errors.deleteKnowledgeContent[content_id] = null
          const response = await useAPI().adminService.delete(
            `/v2/knowledge/documents/${document_id}/contents/${content_id}/tenants/${tenant_id}/env/${env_id}`
          )
          this.trainingDataDetail = this.trainingDataDetail.filter(
            content => content.id !== content_id
          )
          return response.data
        } catch (error: any) {
          this.errors.deleteKnowledgeContent[content_id]
            = error?.response?.data || error
          return false
        } finally {
          this.loadings.deleteKnowledgeContent[content_id] = false
          const { tenant_id, env_id } = targets[0]
          this.checkIfHasAnyUnreflected(tenant_id, env_id)
        }
      })
    },
    setLoadedDocumentToForm() {
      if (!this.selectedKnowledgeDocument) return
      if (this.selectedKnowledgeDocument.original_filename) {
        // File
        this.trainingDataNew.documents = [
          {
            name: this.selectedKnowledgeDocument.name,
            file: null,
            priority: 100,
            label: '',
            labels: [],
            preview: null,
            loadings: {} as Record<string, any>,
            errors: {} as Record<string, any>,
            expand: false,
            success: null
          }
        ]
      } else {
        // Webpage
        this.trainingDataNew.websites = [
          {
            name: this.selectedKnowledgeDocument.name,
            url: this.selectedKnowledgeDocument.source_url!,
            priority: 100,
            label: '',
            labels: [],
            dynamic: this.selectedKnowledgeDocument.extra_settings?.dynamic || false,
            ignore_tags:
              this.selectedKnowledgeDocument.extra_settings?.ignore_tags
              ?? ([] as string[]),
            ignore_classes:
              this.selectedKnowledgeDocument.extra_settings?.ignore_classes
              ?? ([] as string[]),
            preview: null,
            loadings: {} as Record<string, any>,
            errors: {} as Record<string, any>,
            expand: false,
            success: null
          }
        ]
      }
    },
    resetDocuments() {
      this.trainingDataNew.documents = [
        {
          name: '',
          file: null,
          priority: 100,
          label: '',
          labels: [],
          preview: null,
          loadings: {},
          errors: {},
          expand: false,
          success: null,
          // FAQ-related fields
          as_faq: false,
          faq_header_row: [0],
          faq_id_header: null,
          faq_subtitle_header: null,
          faq_question_header: null,
          faq_answer_header: null,
          faq_category_header: null
        }
      ]
    },
    resetWebsites() {
      this.trainingDataNew.websites = [
        {
          name: '',
          url: '',
          priority: 100,
          label: '',
          labels: [],
          dynamic: false,
          ignore_tags: [],
          ignore_classes: [],
          preview: null,
          loadings: {},
          errors: {},
          expand: false,
          success: null
        }
      ]
    },

    async searchRelatedContents(keyword: string) {
      console.log('🚀 ~ searchRelatedContents ~ keyword:', keyword)
      try {
        const { selectedTenantId, selectedEnvId } = useApp()

        this.loadings.searchRelatedContents = true
        this.errors.searchRelatedContents = null
        const response = await useAPI().adminService.get(
          `/v2/knowledge/contents/search/tenants/${selectedTenantId.value}/env/${selectedEnvId.value}`,
          {
            params: {
              keyword
            },
            paramsSerializer: {
              indexes: null
            }
          }
        )
        this.searchContents = response.data?.results
        return response.data?.results
      } catch (error: any) {
        this.errors.searchRelatedContents = error?.response?.data || error
        return false
      } finally {
        this.loadings.searchRelatedContents = false
      }
    },

    async initKnowledgeApi() {
      try {
        this.loadings.initKnowledgeApi = true
        this.errors.initKnowledgeApi = null
        await useAPI().adminService.post('/v2/knowledge/init')
        return true
      } catch (error: any) {
        console.warn('Knowledge init API call failed:', error)
        this.errors.initKnowledgeApi = error?.response?.data || error
        return false
      } finally {
        this.loadings.initKnowledgeApi = false
      }
    },

    addDatasourceDemoData() {
      // Store original data for restoration
      this.originalDatasourceData = {
        trainingDatas: [...this.trainingDatas],
        trainingDatasTotal: this.trainingDatasTotal
      }

      // Add demo datasource data for tour demonstration
      this.trainingDatas = [
        {
          id: 1,
          name: '会社概要・サービス紹介',
          original_filename: 'company_overview.pdf',
          original_context_type: 'pdf',
          source_url: null,
          enabled: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          created_username: 'デモユーザー',
          updated_username: 'デモユーザー',
          isDemo: true
        },
        {
          id: 'demo-datasource-002',
          name: 'よくある質問（FAQ）',
          original_filename: 'faq_data.xlsx',
          original_context_type: 'excel',
          source_url: null,
          enabled: true,
          created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
          updated_at: new Date(Date.now() - 86400000).toISOString(),
          created_username: 'デモユーザー',
          updated_username: 'デモユーザー',
          isDemo: true
        },
        {
          id: 'demo-datasource-003',
          name: '製品マニュアル',
          original_filename: 'product_manual.docx',
          original_context_type: 'word',
          source_url: null,
          enabled: false,
          created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
          updated_at: new Date(Date.now() - 172800000).toISOString(),
          created_username: 'デモユーザー',
          updated_username: 'デモユーザー',
          isDemo: true
        },
        {
          id: 'demo-datasource-004',
          name: '公式ウェブサイト',
          original_filename: null,
          original_context_type: 'web',
          source_url: 'https://example.com',
          enabled: true,
          created_at: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
          updated_at: new Date(Date.now() - 259200000).toISOString(),
          created_username: 'デモユーザー',
          updated_username: 'デモユーザー',
          isDemo: true
        },
        {
          id: 'demo-datasource-005',
          name: '技術仕様書',
          original_filename: 'technical_specs.md',
          original_context_type: 'markdown',
          source_url: null,
          enabled: true,
          created_at: new Date(Date.now() - 345600000).toISOString(), // 4 days ago
          updated_at: new Date(Date.now() - 345600000).toISOString(),
          created_username: 'デモユーザー',
          updated_username: 'デモユーザー',
          isDemo: true
        }
      ]
      this.trainingDatasTotal = 5
    },

    removeDatasourceDemoData() {
      // Restore original data if it exists
      if (this.originalDatasourceData) {
        this.trainingDatas = this.originalDatasourceData.trainingDatas
        this.trainingDatasTotal = this.originalDatasourceData.trainingDatasTotal
        this.originalDatasourceData = null
      } else {
        // Fallback: filter out demo data
        this.trainingDatas = this.trainingDatas.filter((item: any) => !item.isDemo)
        this.trainingDatasTotal = this.trainingDatas.length
      }
    },

    addDatasourceRegisterDemoData() {
      // Add demo files to the form
      const demoFiles = [
        {
          name: 'サンプル製品マニュアル',
          file: new File(['製品の使用方法とトラブルシューティングガイド'], 'product_manual.pdf', { type: 'application/pdf' }),
          priority: 1,
          label: '',
          labels: ['製品情報', 'マニュアル'],
          preview: null,
          loadings: {} as Record<string, any>,
          errors: {} as Record<string, any>,
          expand: false,
          success: null,
          as_faq: false,
          faq_header_row: [0],
          faq_id_header: null,
          faq_subtitle_header: null,
          faq_question_header: null,
          faq_answer_header: null,
          faq_category_header: null
        },
        {
          name: 'FAQ集',
          file: new File(['よくある質問と回答集'], 'faq.docx', { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }),
          priority: 2,
          label: '',
          labels: ['FAQ', 'サポート'],
          preview: null,
          loadings: {} as Record<string, any>,
          errors: {} as Record<string, any>,
          expand: false,
          success: null,
          as_faq: false,
          faq_header_row: [0],
          faq_id_header: null,
          faq_subtitle_header: null,
          faq_question_header: null,
          faq_answer_header: null,
          faq_category_header: null
        }
      ]

      // Add demo websites
      const demoWebsites = [
        {
          name: '会社公式サイト',
          url: 'https://example.com',
          priority: 1,
          label: '',
          labels: [] as string[],
          dynamic: false,
          ignore_tags: ['nav', 'footer', 'aside'] as string[],
          ignore_classes: [] as string[],
          preview: null,
          loadings: {} as Record<string, any>,
          errors: {} as Record<string, any>,
          expand: false,
          success: null
        }
      ]

      // Set demo data to form
      this.trainingDataNew.documents = demoFiles
      this.trainingDataNew.websites = demoWebsites
    },

    removeDatasourceRegisterDemoData() {
      // Reset form data
      this.resetDocuments()
      this.resetWebsites()
    },

    addKnowledgeListDemoData() {
      // Add demo knowledge detail data for tour demonstration
      this.trainingDataDetail = [
        {
          id: 'demo-knowledge-001',
          document_id: 'demo-datasource-001',
          blob_path: 'company_overview.pdf/page_1',
          content: '当社は2010年に設立された技術革新企業です。AI・機械学習分野において最先端のソリューションを提供し、お客様のビジネス成長をサポートしています。主力製品には自然言語処理エンジン、画像認識システム、予測分析ツールがあります。',
          priority: 1,
          label: ['会社情報', '概要'],
          enabled: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isDemo: true
        },
        {
          id: 'demo-knowledge-002',
          document_id: 'demo-datasource-001',
          blob_path: 'company_overview.pdf/page_2',
          content: '私たちのミッション：テクノロジーの力で社会課題を解決し、より良い未来を創造すること。ビジョン：AI技術のリーディングカンパニーとして、世界中の企業のデジタル変革を推進する。',
          priority: 2,
          label: ['会社情報', 'ミッション'],
          enabled: true,
          created_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
          updated_at: new Date(Date.now() - 3600000).toISOString(),
          isDemo: true
        },
        {
          id: 'demo-knowledge-003',
          document_id: 'demo-datasource-002',
          blob_path: 'faq_data.xlsx/row_1',
          content: 'Q: サービスの利用料金はいくらですか？\nA: 基本プランは月額10,000円から、プロフェッショナルプランは月額50,000円からご利用いただけます。詳細な料金体系については営業担当までお問い合わせください。',
          priority: 1,
          label: ['FAQ', '料金'],
          enabled: true,
          created_at: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
          updated_at: new Date(Date.now() - 7200000).toISOString(),
          isDemo: true
        },
        {
          id: 'demo-knowledge-004',
          document_id: 'demo-datasource-002',
          blob_path: 'faq_data.xlsx/row_2',
          content: 'Q: 無料トライアルはありますか？\nA: はい、14日間の無料トライアルをご提供しています。トライアル期間中は全ての機能をお試しいただけます。お申し込みはウェブサイトから簡単に行えます。',
          priority: 2,
          label: ['FAQ', 'トライアル'],
          enabled: false,
          created_at: new Date(Date.now() - 10800000).toISOString(), // 3 hours ago
          updated_at: new Date(Date.now() - 10800000).toISOString(),
          isDemo: true
        },
        {
          id: 'demo-knowledge-005',
          document_id: 'demo-datasource-003',
          blob_path: 'product_manual.docx/section_1',
          content: '製品の初期設定手順：1. アカウント作成、2. API キーの取得、3. 開発環境の構築、4. サンプルコードの実行。詳細な手順については各章をご参照ください。',
          priority: 3,
          label: ['マニュアル', '初期設定'],
          enabled: true,
          created_at: new Date(Date.now() - 14400000).toISOString(), // 4 hours ago
          updated_at: new Date(Date.now() - 14400000).toISOString(),
          isDemo: true
        },
        {
          id: 'demo-knowledge-006',
          document_id: 'demo-datasource-004',
          blob_path: 'https://example.com/about',
          content: '会社概要ページの内容：設立年月日、所在地、代表者情報、事業内容、沿革などの基本情報を掲載しています。最新の企業情報については定期的に更新されています。',
          priority: 1,
          label: ['ウェブサイト', '会社情報'],
          enabled: true,
          created_at: new Date(Date.now() - 18000000).toISOString(), // 5 hours ago
          updated_at: new Date(Date.now() - 18000000).toISOString(),
          isDemo: true
        },
        {
          id: 'demo-knowledge-007',
          document_id: 'demo-datasource-005',
          blob_path: 'technical_specs.md/api_section',
          content: 'API仕様書：RESTful APIを提供しており、JSON形式でのデータ交換をサポートしています。認証にはOAuth 2.0を使用し、レート制限は1時間あたり1000リクエストです。',
          priority: 2,
          label: ['技術仕様', 'API'],
          enabled: true,
          created_at: new Date(Date.now() - 21600000).toISOString(), // 6 hours ago
          updated_at: new Date(Date.now() - 21600000).toISOString(),
          isDemo: true
        },
        {
          id: 'demo-knowledge-008',
          document_id: 'demo-datasource-005',
          blob_path: 'technical_specs.md/security_section',
          content: 'セキュリティ仕様：データ暗号化（AES-256）、通信暗号化（TLS 1.3）、アクセス制御（RBAC）、監査ログ機能を実装しています。SOC 2 Type II認証を取得済みです。',
          priority: 1,
          label: ['技術仕様', 'セキュリティ'],
          enabled: false,
          created_at: new Date(Date.now() - 25200000).toISOString(), // 7 hours ago
          updated_at: new Date(Date.now() - 25200000).toISOString(),
          isDemo: true
        }
      ]

      // Set selected training data for demo
      this.selectedTrainingData = {
        id: 'demo-datasource-001',
        name: '会社概要・サービス紹介',
        original_filename: 'company_overview.pdf',
        original_context_type: 'pdf',
        source_url: null,
        enabled: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_username: 'デモユーザー',
        updated_username: 'デモユーザー',
        isDemo: true
      }
    },

    removeKnowledgeListDemoData() {
      // Reset knowledge detail data
      this.trainingDataDetail = []
      this.selectedTrainingData = null
    }
  }
})
