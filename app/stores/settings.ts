import {
  SUPPORTED_LANGUAGES,
  DEFAULT_ERROR_MESSAGES
} from '~/constants/supported-languages'

export const useSettingsStore = defineStore('settingsStore', {
  persist: {
    pick: [],
    storage: window?.localStorage
  },
  state: () => ({
    basicSettings: {} as Record<string, any>,
    customSettings: {} as Record<string, any>,
    weatherSettings: {} as Record<string, any>,
    errorSettings: {} as Record<string, any>,
    allRagErrorSettings: [] as any[],
    featureSettings: {} as Record<string, any>,
    originalFeatureSettings: {} as Record<string, any>,
    isNewErrorModalOpen: false,
    originalBaseCustomSettings: null as any,
    loadings: {
      updateBasicSetting: {} as Record<string, boolean>,
      updateCustomSetting: {} as Record<string, boolean>,
      updateErrorSetting: {} as Record<string, boolean>,
      fetchBasicSettings: {} as Record<string, boolean>,
      fetchFeatureSettings: false,
      updateFeatureSettings: false,
      createErrorSetting: false,
      deleteErrorSetting: false,
      resetErrorSettings: false
    } as Record<string, any>,
    errors: {
      updateCustomSetting: {} as Record<string, any>,
      updateErrorSetting: {} as Record<string, any>,
      updateBasicSetting: {} as Record<string, any>,
      updateFeatureSettings: {} as Record<string, any>,
      createErrorSetting: null,
      deleteErrorSetting: null,
      resetErrorSettings: null
    } as Record<string, any>,
    supportLanguages: [] as Record<string, any>
  }),
  getters: {
    supportedLanguagesOptions(): any[] {
      return SUPPORTED_LANGUAGES.map(lang => ({
        id: lang.id,
        label: lang.label,
        icon: lang.icon
      }))
    },
    currentColorPrimary(): string {
      const { selectedTenantId, selectedEnvId } = useApp()
      return this.customSettings?.[selectedTenantId.value]?.[
        selectedEnvId.value
      ]?.color_primary
    },
    currentChatbotAvatarURL(): string {
      const { selectedTenantId, selectedEnvId } = useApp()
      return this.customSettings?.[selectedTenantId.value]?.[
        selectedEnvId.value
      ]?.avatar_url
    },
    currentChatbotName(): string {
      const { selectedTenantId, selectedEnvId } = useApp()
      return (
        this.basicSettings?.[selectedTenantId.value]?.[selectedEnvId.value]
          ?.name || 'チャットボット'
      )
    },
    survey_option_count(): number {
      const { selectedTenantId, selectedEnvId } = useApp()
      return (
        this.basicSettings?.[selectedTenantId.value]?.[selectedEnvId.value]
          ?.survey_option_count || 5
      )
    }
  },
  actions: {
    async fetchBasicSettings(tenant_id: string, env_id: string) {
      if (this.basicSettings[tenant_id]?.[env_id]) {
        return true
      }
      try {
        this.loadings.fetchBasicSettings = true
        this.basicSettings[tenant_id] = {}
        this.errors.fetchBasicSettings = null
        const response = await useAPI().adminService.get(
          `/v2/basicSettings/tenants/${tenant_id}/env/${env_id}`
        )
        this.basicSettings[tenant_id][env_id] = response.data
        return true
      } catch (error: any) {
        this.errors.fetchBasicSettings = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchBasicSettings = false
      }
    },
    async createBasicSetting(tenant_id: string, env_id: string, payload: any) {
      try {
        this.loadings.createBasicSetting = true
        this.errors.createBasicSetting = null
        const response = await useAPI().adminService.post(
          `/v2/basicSettings/tenants/${tenant_id}/env/${env_id}`,
          payload
        )
        if (!this.basicSettings[tenant_id]) {
          this.basicSettings[tenant_id] = {}
        }

        this.basicSettings[tenant_id][env_id] = response.data
        return true
      } catch (error: any) {
        this.errors.createBasicSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.createBasicSetting = false
      }
    },
    async updateBasicSetting(
      tenant_id: string,
      env_id: string,
      key: string,
      value: any
    ) {
      try {
        this.loadings.updateBasicSetting[key] = true
        this.errors.updateBasicSetting[key] = null
        const response = await useAPI().adminService.put(
          `/v2/basicSettings/tenants/${tenant_id}/env/${env_id}`,
          {
            ...this.basicSettings[tenant_id][env_id],
            [key]: value
          }
        )
        this.basicSettings[tenant_id][env_id] = response.data
        return true
      } catch (error: any) {
        this.errors.updateBasicSetting[key] = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateBasicSetting[key] = false
      }
    },
    async deleteBasicSetting(tenant_id: string, env_id: string) {
      try {
        this.loadings.deleteBasicSetting = true
        this.errors.deleteBasicSetting = null
        await useAPI().adminService.delete(
          `/v2/basicSettings/tenants/${tenant_id}/env/${env_id}`
        )
        this.basicSettings[tenant_id][env_id] = null
        return true
      } catch (error: any) {
        this.errors.deleteBasicSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteBasicSetting = false
      }
    },
    async fetchCustomSettings(tenant_id: string, env_id: string) {
      if (this.customSettings[tenant_id]?.[env_id]) {
        return true
      }
      try {
        this.loadings.fetchCustomSettings = true
        this.customSettings[tenant_id] = {}
        this.errors.fetchCustomSettings = null
        const response = await useAPI().adminService.get(
          `/v2/customSettings/tenants/${tenant_id}/env/${env_id}`
        )
        this.customSettings[tenant_id][env_id] = response.data?.settings
        // set primary color to response.data?.settings?.custom?.color_primary
        const appConfig = useAppConfig()
        appConfig.ui.primary = response.data?.settings?.color_primary
        return true
      } catch (error: any) {
        this.errors.fetchCustomSettings = error?.response?.data || error
        // check if error_code: 4241
        // if (error?.response?.data?.error_code === 4241) {
        //   return await this.createCustomSetting(tenant_id, env_id, {})
        // }
        return false
      } finally {
        this.loadings.fetchCustomSettings = false
      }
    },
    async createCustomSetting(tenant_id: string, env_id: string, payload: any) {
      try {
        this.loadings.createCustomSetting = true
        this.errors.createCustomSetting = null
        const response = await useAPI().adminService.post(
          `/v2/customSettings/tenants/${tenant_id}/env/${env_id}`,
          payload
        )
        if (!this.customSettings[tenant_id]) {
          this.customSettings[tenant_id] = {}
        }
        this.customSettings[tenant_id][env_id] = response.data?.settings
        return true
      } catch (error: any) {
        this.errors.createCustomSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.createCustomSetting = false
      }
    },
    async updateCustomSetting(
      tenant_id: string,
      env_id: string,
      key: string,
      value: any
    ) {
      try {
        this.loadings.updateCustomSetting[key] = true
        this.errors.updateCustomSetting[key] = null
        const response = await useAPI().adminService.put(
          `/v2/customSettings/tenants/${tenant_id}/env/${env_id}`,
          {
            settings: {
              ...this.customSettings[tenant_id][env_id],
              [key]: value
            }
          }
        )
        this.customSettings[tenant_id][env_id] = response.data?.settings
        return true
      } catch (error: any) {
        this.errors.updateCustomSetting[key] = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateCustomSetting[key] = false
      }
    },
    async deleteCustomSetting(tenant_id: string, env_id: string) {
      try {
        this.loadings.deleteCustomSetting = true
        this.errors.deleteCustomSetting = null
        await useAPI().adminService.delete(
          `/v2/customSettings/tenants/${tenant_id}/env/${env_id}`
        )
        this.customSettings[tenant_id][env_id] = null
        return true
      } catch (error: any) {
        this.errors.deleteCustomSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteCustomSetting = false
      }
    },
    async fetchAllSupportLanguages(tenant_id: string, env_id: string) {
      try {
        this.loadings.fetchAllSupportLanguages = true
        this.supportLanguages[tenant_id] = {}
        this.errors.fetchAllSupportLanguages = null
        const response = await useAPI().adminService.get(
          `/v2/language/all/tenants/${tenant_id}/env/${env_id}`
        )
        this.supportLanguages[tenant_id][env_id]
          = response.data?.languages || []
        return true
      } catch (error: any) {
        this.errors.fetchAllSupportLanguages = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchAllSupportLanguages = false
      }
    },
    async addSupportLanguage(
      tenant_id: string,
      env_id: string,
      language: string
    ) {
      try {
        this.loadings.addSupportLanguage = true
        this.errors.addSupportLanguage = null
        const response = await useAPI().adminService.post(
          `/v2/language/tenants/${tenant_id}/env/${env_id}`,
          {
            language
          }
        )
        this.supportLanguages[tenant_id][env_id].push(response.data)
        return true
      } catch (error: any) {
        this.errors.addSupportLanguage = error?.response?.data || error
        return false
      } finally {
        this.loadings.addSupportLanguage = false
      }
    },
    addSupportLanguageDemo() {
      const { selectedTenantId, selectedEnvId } = useApp()
      this.supportLanguages[selectedTenantId.value][selectedEnvId.value].push({
        description: null,
        enabled: true,
        env_id: selectedEnvId.value,
        tenant_id: selectedTenantId.value,
        language: '英語',
        created_username: 'demo',
        updated_username: 'demo',
        created_at: '2025-06-18T10:45:47.946100+09:00',
        updated_at: '2025-06-18T10:45:47.946100+09:00',
        isDemo: true
      })
    },
    removeSupportLanguageDemo() {
      const { selectedTenantId, selectedEnvId } = useApp()
      this.supportLanguages[selectedTenantId.value][selectedEnvId.value]
        = this.supportLanguages[selectedTenantId.value][
          selectedEnvId.value
        ].filter((lang: any) => !lang.isDemo)
    },
    async removeSupportLanguage(
      tenant_id: string,
      env_id: string,
      language: string
    ) {
      try {
        this.loadings.removeSupportLanguage = true
        this.errors.removeSupportLanguage = null
        await useAPI().adminService.delete(
          `/v2/language/${language}/tenants/${tenant_id}/env/${env_id}`
        )
        this.supportLanguages[tenant_id][env_id] = this.supportLanguages[
          tenant_id
        ][env_id].filter((lang: any) => lang.language !== language)
        return true
      } catch (error: any) {
        this.errors.removeSupportLanguage = error?.response?.data || error
        return false
      } finally {
        this.loadings.removeSupportLanguage = false
      }
    },
    async setDefaultLanguage(
      tenant_id: string,
      env_id: string,
      language: string
    ) {
      try {
        this.loadings.setDefaultLanguage = true
        this.errors.setDefaultLanguage = null
        const response = await useAPI().adminService.put(
          `/v2/basicSettings/tenants/${tenant_id}/env/${env_id}`,
          {
            ...this.basicSettings[tenant_id][env_id],
            default_language: language
          }
        )
        this.basicSettings[tenant_id][env_id] = response.data
        return true
      } catch (error: any) {
        this.errors.setDefaultLanguage = error?.response?.data || error
        return false
      } finally {
        this.loadings.setDefaultLanguage = false
      }
    },

    async fetchAllRagErrorSettings() {
      try {
        this.loadings.fetchAllRagErrorSettings = true
        this.errors.fetchAllRagErrorSettings = null
        const response = await useAPI().adminService.get(
          `/v2/errorSettings/rag/all`
        )
        this.allRagErrorSettings = response.data?.errors || []
        return true
      } catch (error: any) {
        this.errors.fetchAllRagErrorSettings = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchAllRagErrorSettings = false
      }
    },

    async fetchErrorSettings(tenant_id: string, env_id: string) {
      try {
        this.loadings.fetchErrorSettings = true
        this.errorSettings[tenant_id] = {}
        this.errors.fetchErrorSettings = null
        const response = await useAPI().adminService.get(
          `/v2/errorSettings/all/tenants/${tenant_id}/env/${env_id}`
        )
        this.errorSettings[tenant_id][env_id] = response.data?.errors || []
        // check if empty
        if (!this.errorSettings[tenant_id][env_id].length) {
          return await this.createDefaultErrorSettings(tenant_id, env_id)
        }
        return true
      } catch (error: any) {
        this.errors.fetchErrorSettings = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchErrorSettings = false
      }
    },

    async createDefaultErrorSettings(tenant_id: string, env_id: string) {
      try {
        this.loadings.createDefaultErrorSettings = true
        this.errorSettings[tenant_id] = {}
        await Promise.all(
          Object.entries(DEFAULT_ERROR_MESSAGES).map(([key, value]) =>
            useAPI().adminService.post(
              `/v2/errorSettings/tenants/${tenant_id}/env/${env_id}`,
              {
                error_code: key,
                message: value
              }
            )
          )
        )
        this.errorSettings[tenant_id][env_id] = Object.entries(
          DEFAULT_ERROR_MESSAGES
        ).map(([key, value]) => ({
          error_code: key,
          message: value
        }))
        return true
      } catch (error: any) {
        this.errors.createDefaultErrorSettings = error?.response?.data || error
        return false
      } finally {
        this.loadings.createDefaultErrorSettings = false
      }
    },

    async updateErrorSetting(
      tenant_id: string,
      env_id: string,
      error_code: string,
      message: string
    ) {
      try {
        this.loadings.updateErrorSetting[error_code] = true
        this.errors.updateErrorSetting[error_code] = null
        await useAPI().adminService.put(
          `/v2/errorSettings/${error_code}/tenants/${tenant_id}/env/${env_id}`,
          {
            message
          }
        )
        this.errorSettings[tenant_id][env_id] = this.errorSettings[tenant_id][
          env_id
        ].map((error: any) => {
          if (error.error_code === error_code) {
            return {
              ...error,
              message
            }
          }
          return error
        })
        return true
      } catch (error: any) {
        this.errors.updateErrorSetting[error_code]
          = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateErrorSetting[error_code] = false
      }
    },

    async createErrorSetting(
      tenant_id: string,
      env_id: string,
      error_code: string,
      message: string
    ) {
      try {
        this.loadings.createErrorSetting = true
        this.errors.createErrorSetting = null
        await useAPI().adminService.post(
          `/v2/errorSettings/tenants/${tenant_id}/env/${env_id}`,
          {
            error_code,
            message
          }
        )

        // Add the new error setting to the local state
        if (!this.errorSettings[tenant_id]) {
          this.errorSettings[tenant_id] = {}
        }
        if (!this.errorSettings[tenant_id][env_id]) {
          this.errorSettings[tenant_id][env_id] = []
        }

        // Find description from allRagErrorSettings
        const errorInfo = this.allRagErrorSettings.find(
          e => e.error_code.toString() === error_code.toString()
        )

        this.errorSettings[tenant_id][env_id].push({
          error_code,
          message,
          description: errorInfo?.description,
          description_jp: errorInfo?.description_jp
        })

        return true
      } catch (error: any) {
        this.errors.createErrorSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.createErrorSetting = false
      }
    },

    async deleteErrorSetting(
      tenant_id: string,
      env_id: string,
      error_code: string
    ) {
      try {
        this.loadings.deleteErrorSetting = true
        this.errors.deleteErrorSetting = null
        await useAPI().adminService.delete(
          `/v2/errorSettings/${error_code}/tenants/${tenant_id}/env/${env_id}`
        )

        // Remove the error setting from the local state
        if (
          this.errorSettings[tenant_id]
          && this.errorSettings[tenant_id][env_id]
        ) {
          this.errorSettings[tenant_id][env_id] = this.errorSettings[tenant_id][
            env_id
          ].filter((error: any) => error.error_code !== error_code)
        }

        return true
      } catch (error: any) {
        this.errors.deleteErrorSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteErrorSetting = false
      }
    },

    async resetErrorSettings(tenant_id: string, env_id: string) {
      try {
        this.loadings.resetErrorSettings = true
        this.errors.resetErrorSettings = null

        // First delete all existing error settings
        if (
          this.errorSettings[tenant_id]
          && this.errorSettings[tenant_id][env_id]
        ) {
          const deletePromises = this.errorSettings[tenant_id][env_id].map(
            (error: any) =>
              this.deleteErrorSetting(tenant_id, env_id, error.error_code)
          )
          await Promise.all(deletePromises)
        }

        // Then create default error settings
        await this.createDefaultErrorSettings(tenant_id, env_id)

        // Refresh error settings and descriptions
        await this.fetchAllRagErrorSettings()
        await this.fetchErrorSettings(tenant_id, env_id)

        return true
      } catch (error: any) {
        this.errors.resetErrorSettings = error?.response?.data || error
        return false
      } finally {
        this.loadings.resetErrorSettings = false
      }
    },

    async fetchWeatherSettings(tenant_id: string, env_id: string) {
      if (this.weatherSettings[tenant_id]?.[env_id]) {
        return true
      }
      try {
        this.loadings.fetchWeatherSettings = true
        this.weatherSettings[tenant_id] = {}
        this.errors.fetchWeatherSettings = null
        const response = await useAPI().adminService.get(
          `v2/weather/all/tenants/${tenant_id}/env/${env_id}`
        )
        this.weatherSettings[tenant_id][env_id] = response.data?.weather
        return true
      } catch (error: any) {
        this.errors.fetchWeatherSettings = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchWeatherSettings = false
      }
    },

    async addWeatherSetting(
      tenant_id: string,
      env_id: string,
      location: string
    ) {
      try {
        this.loadings.addWeatherSetting = true
        this.errors.addWeatherSetting = null
        const response = await useAPI().adminService.post(
          `/v2/weather/tenants/${tenant_id}/env/${env_id}`,
          {
            location
          }
        )
        this.weatherSettings[tenant_id][env_id].push(response.data)
        return true
      } catch (error: any) {
        this.errors.addWeatherSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.addWeatherSetting = false
      }
    },

    async deleteWeatherSetting(
      tenant_id: string,
      env_id: string,
      location: string
    ) {
      try {
        this.loadings.deleteWeatherSetting = true
        this.errors.deleteWeatherSetting = null
        await useAPI().adminService.delete(
          `/v2/weather/${location}/tenants/${tenant_id}/env/${env_id}`
        )
        this.weatherSettings[tenant_id][env_id] = this.weatherSettings[
          tenant_id
        ][env_id].filter((weather: any) => weather.location !== location)
        return true
      } catch (error: any) {
        this.errors.deleteWeatherSetting = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteWeatherSetting = false
      }
    },

    // Feature Settings

    resetFeatureSettings(tenant_id: string, env_id: string) {
      this.featureSettings[tenant_id][env_id] = {
        ...this.originalFeatureSettings[tenant_id][env_id]
      }
    },
    async fetchFeatureSettings(tenant_id: string, env_id: string) {
      if (
        this.featureSettings[tenant_id]?.[env_id]
        && this.originalFeatureSettings[tenant_id]?.[env_id]
      ) {
        return true
      }
      try {
        this.loadings.fetchFeatureSettings = true
        this.featureSettings[tenant_id] = {}
        this.originalFeatureSettings[tenant_id] = {}
        this.errors.fetchFeatureSettings = null
        const response = await useAPI().adminService.get(
          `/v2/featureSettings/tenants/${tenant_id}/env/${env_id}`
        )
        const targetData = response.data
        this.featureSettings[tenant_id][env_id] = targetData
        this.originalFeatureSettings[tenant_id][env_id] = { ...targetData }
        return true
      } catch (error: any) {
        console.error(error)
        this.errors.fetchFeatureSettings = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchFeatureSettings = false
      }
    },
    async createFeatureSettings(
      tenant_id: string,
      env_id: string,
      payload: any
    ) {
      try {
        this.loadings.updateFeatureSettings = true
        this.errors.createFeatureSettings = null
        const response = await useAPI().adminService.post(
          `/v2/featureSettings/tenants/${tenant_id}/env/${env_id}`,
          payload
        )
        if (!this.featureSettings[tenant_id]) {
          this.featureSettings[tenant_id] = {}
        }

        this.featureSettings[tenant_id][env_id] = response.data
        return true
      } catch (error: any) {
        this.errors.createFeatureSettings = error?.response?.data || error
        this.resetFeatureSettings(tenant_id, env_id)
        return false
      } finally {
        this.loadings.updateFeatureSettings = false
      }
    },
    async updateFeatureSettings(
      tenant_id: string,
      env_id: string,
      payload: any
    ) {
      const targetFeatureSettings = this.featureSettings[tenant_id][env_id]
      try {
        this.loadings.updateFeatureSettings = true
        this.errors.updateFeatureSettings = null
        if (!targetFeatureSettings.created_at) {
          console.log('Should create first.')
          return await this.createFeatureSettings(tenant_id, env_id, {
            ...targetFeatureSettings,
            ...payload
          })
        } else {
          const response = await useAPI().adminService.put(
            `/v2/featureSettings/tenants/${tenant_id}/env/${env_id}`,
            {
              ...targetFeatureSettings,
              ...payload
            }
          )
          this.featureSettings[tenant_id][env_id] = response.data
        }
        return true
      } catch (error: any) {
        this.errors.updateFeatureSettings = error?.response?.data || error
        this.resetFeatureSettings(tenant_id, env_id)
        return false
      } finally {
        this.loadings.updateFeatureSettings = false
      }
    },
    async deleteFeatureSettings(tenant_id: string, env_id: string) {
      try {
        this.loadings.updateFeatureSettings = true
        this.errors.deleteFeatureSettings = null
        await useAPI().adminService.delete(
          `/v2/featureSettings/tenants/${tenant_id}/env/${env_id}`
        )
        this.featureSettings[tenant_id][env_id] = null
        this.originalFeatureSettings[tenant_id][env_id] = null
        return true
      } catch (error: any) {
        this.errors.deleteFeatureSettings = error?.response?.data || error
        this.resetFeatureSettings(tenant_id, env_id)
        return false
      } finally {
        this.loadings.updateFeatureSettings = false
      }
    },
    addDemoSetting() {
      const { selectedTenantId } = useApp()
      const tenant_id = selectedTenantId.value
      this.originalBaseCustomSettings = {
        basicSettings: this.basicSettings,
        customSettings: this.customSettings
      }
      this.customSettings[tenant_id] = {}
      this.customSettings[tenant_id]['demo_dev'] = {
        avatar_url: 'https://png.pngtree.com/png-clipart/20230429/original/pngtree-robot-artificial-intelligence-technology-transparent-png-image_9123453.png',
        color_primary: 'pink',
        isDemo: true
      }
      this.customSettings[tenant_id]['demo_prod'] = {
        avatar_url: 'https://png.pngtree.com/png-clipart/20230429/original/pngtree-robot-artificial-intelligence-technology-transparent-png-image_9123453.png',
        color_primary: 'green',
        isDemo: true
      }
      this.basicSettings[tenant_id]['demo_dev'] = {
        created_at: new Date().toISOString(),
        created_username: 'tanaka',
        default_language: '日本語',
        env_id: 'demo_dev',
        name: 'デモ店 検証',
        survey_option_count: 2,
        tenant_id: tenant_id,
        updated_at: new Date().toISOString(),
        updated_username: 'morita',
        welcome_message: '\n<p>\n Welcome',
        isDemo: true
      }
      this.basicSettings[tenant_id]['demo_prod'] = {
        created_at: new Date().toISOString(),
        created_username: 'tanaka',
        default_language: '日本語',
        env_id: 'demo_prod',
        name: 'デモ店 本番',
        survey_option_count: 1,
        tenant_id: tenant_id,
        updated_at: new Date().toISOString(),
        updated_username: 'morita',
        welcome_message: '\n<p>\n Hello',
        isDemo: true
      }
    },
    removeDemoSetting() {
      if (this.originalBaseCustomSettings) {
        this.basicSettings = this.originalBaseCustomSettings.basicSettings
        this.customSettings = this.originalBaseCustomSettings.customSettings
        this.originalBaseCustomSettings = null
      } else {
        this.basicSettings = {}
        this.customSettings = {}
      }
    }
  }
})
