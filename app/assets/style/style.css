/* Import tour styles */
@import '../scss/tour.scss';

body {
    font-family: "游ゴシック体", YuGothic, "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック", "Yu Gothic", sans-serif;
}

.scrollbar-thin {
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    scrollbar-width: thin;
}


.scrollbar-thin::-webkit-scrollbar-track,
body::-webkit-scrollbar-track {
    border-radius: 25px !important;
    cursor: pointer !important;
    background: transparent !important;
}

.scrollbar-thin::-webkit-scrollbar-thumb,
body::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1) !important;
    border-radius: 25px !important;
}

.scrollbar-only-hover::-webkit-scrollbar-thumb {
    background: transparent !important;
}

.scrollbar-only-hover:hover::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1) !important;
}
