<script setup lang="ts">
const route = useRoute()
const router = useRouter()
const { selectedTenantId, selectedEnvId } = useApp()
const environmentsStore = useEnvironmentsStore()
const {
  remainingEnv,
  selectedEnv,
  isDevAndProdHasSameVersion,
  isDevVersionOlderThanProd
} = storeToRefs(environmentsStore)
const deploymentsStore = useDeploymentsStore()
const { loadings, latestDeployment } = storeToRefs(deploymentsStore)
const deployMode = ref({
  swap: false,
  sync: false
})
const settingsStore = useSettingsStore()

const onChangeDeployMode = (mode: string) => {
  deployMode.value = {
    swap: mode === 'swap',
    sync: mode === 'sync'
  }
}

const deployModeIcon = computed(() => {
  if (loadings.value['swapEnv'] || loadings.value['syncProdEnvToDev']) {
    return 'eos-icons:three-dots-loading'
  } else if ([0, 1].includes(latestDeployment.value?.status)) {
    return 'line-md:downloading-loop'
  } else if (deployMode.value.swap) {
    return 'uiw:swap'
  } else if (deployMode.value.sync) {
    return 'lucide:move-left'
  }
  return 'radix-icons:border-dotted'
})

const devEnv = computed(() => {
  return selectedEnv.value?.environment === 2
    ? selectedEnv.value
    : remainingEnv.value
})

const prodEnv = computed(() => {
  return selectedEnv.value?.environment === 1
    ? selectedEnv.value
    : remainingEnv.value
})

// Check if staging version is higher than production version
const isStagingVersionHigher = computed(() => {
  const stagingVersion = devEnv.value?.version
  const productionVersion = prodEnv.value?.version

  if (!stagingVersion || !productionVersion) {
    return false
  }

  // Compare versions as numbers (assuming they are numeric)
  const stagingNum = parseFloat(stagingVersion)
  const productionNum = parseFloat(productionVersion)

  return stagingNum > productionNum
})

const confirm = useConfirm()
const toast = useToast()
const onStartDeploy = () => {
  confirm.show({
    title: deployMode.value.swap ? 'スワップの確認' : '同期の確認',
    description: deployMode.value.swap
      ? 'スワップを実行しますか？'
      : '同期を実行しますか？',
    confirmText: deployMode.value.swap ? 'スワップ' : '同期',
    onConfirm: async () => {
      if (deployMode.value.swap) {
        confirm.open.value = false
        const result = await deploymentsStore.swapEnv(
          selectedTenantId.value,
          devEnv.value?.id
        )
        if (result) {
          toast.add({
            title: 'スワップが完了しました',
            color: 'green',
            icon: 'lets-icons:check-fill'
          })
          // fetchAllEnvs
          await environmentsStore.fetchAllEnvs(selectedTenantId.value, true)
          selectedEnvId.value = remainingEnv.value.id

          // replace params tenantId
          router.replace({
            params: {
              env: selectedEnvId.value
            },
            query: {
              swap: 'true'
            }
          })
        }
      } else if (deployMode.value.sync) {
        const result = await deploymentsStore.syncProdEnvToDev(
          selectedTenantId.value,
          devEnv.value?.id
        )
        if (result) {
          toast.add({
            title: '同期処理が登録されました',
            color: 'green',
            icon: 'lets-icons:check-fill'
          })
          // fetchAllEnvs
          await environmentsStore.fetchAllEnvs(selectedTenantId.value, true)
        }
      }
    }
  })
}

// Refresh function to update version information
const refreshVersionInfo = async (showToast = false) => {
  try {
    await Promise.all([
      deploymentsStore.getLatestDeployment(selectedTenantId.value),
      environmentsStore.fetchAllEnvs(selectedTenantId.value, true),
      settingsStore.fetchCustomSettings(selectedTenantId.value, remainingEnv.value?.id || ''),
      settingsStore.fetchBasicSettings(selectedTenantId.value, remainingEnv.value?.id || ''),
      settingsStore.fetchCustomSettings(selectedTenantId.value, selectedEnvId.value),
      settingsStore.fetchBasicSettings(selectedTenantId.value, selectedEnvId.value)
    ])

    if (showToast) {
      toast.add({
        title: 'バージョン情報を更新しました',
        color: 'green',
        icon: 'heroicons:check-circle'
      })
    }
  } catch (error) {
    if (showToast) {
      toast.add({
        title: 'バージョン情報の更新に失敗しました',
        color: 'red',
        icon: 'heroicons:exclamation-circle'
      })
    }
  }
}

onMounted(() => {
  refreshVersionInfo()
})

// Auto-refresh when page becomes active (e.g., navigating from other pages)
onActivated(() => {
  refreshVersionInfo()
})

// Auto-refresh when browser tab becomes active
const { isSupported, visibility } = useDocumentVisibility()
if (isSupported) {
  watch(visibility, (current) => {
    if (current === 'visible') {
      refreshVersionInfo()
    }
  })
}

const syncHelpText = computed(() => {
  if (isDevAndProdHasSameVersion.value) {
    return '本番環境と検証環境のバージョンが同じです。'
  }
  if (isDevVersionOlderThanProd.value) {
    return '検証環境のバージョンが古いです。'
  }
  if ([0].includes(latestDeployment.value?.status)) {
    return '同期処理を待ちます。'
  }
  if ([1].includes(latestDeployment.value?.status)) {
    return '同期処理を実行中です。'
  }
  return '同期を実行します。'
})

const swapHelpTest = computed(() => {
  if (isDevAndProdHasSameVersion.value) {
    return '本番環境と検証環境のバージョンが同じです。'
  }
  if ([0].includes(latestDeployment.value?.status)) {
    return '同期待ちのため、スワップ不可'
  }
  if ([1].includes(latestDeployment.value?.status)) {
    return '同期処理中のため、スワップ不可'
  }
  return 'スワップを実行します。'
})

const intervalInstance = ref()
watch(
  () => latestDeployment.value,
  async (newlatestDeployment, prevLatestDeployment) => {
    if ([0, 1].includes(latestDeployment.value?.status)) {
      if (intervalInstance.value) {
        clearInterval(intervalInstance.value)
      }
      intervalInstance.value = setInterval(() => {
        deploymentsStore.getLatestDeployment(selectedTenantId.value)
      }, 30000)
    } else if (
      [2].includes(newlatestDeployment?.status)
      && [1, 0].includes(prevLatestDeployment?.status)
    ) {
      if (intervalInstance.value) {
        clearInterval(intervalInstance.value)
      }
      await environmentsStore.fetchAllEnvs(selectedTenantId.value, true)
      router.replace({
        params: {
          env: selectedEnvId.value
        },
        query: {
          sync: 'true'
        }
      })
    } else {
      if (intervalInstance.value) {
        clearInterval(intervalInstance.value)
      }
    }
  },
  { immediate: true, deep: true }
)

onUnmounted(() => {
  if (intervalInstance.value) {
    clearInterval(intervalInstance.value)
  }
})

const onCancelSync = async () => {
  confirm.show({
    title: '同期キャンセルの確認',
    description: '同期をキャンセルしますか？',
    confirmText: 'キャンセル',
    onConfirm: async () => {
      const result = await deploymentsStore.cancelSyncProdEnvToDev(
        selectedTenantId.value
      )
      if (result) {
        toast.add({
          title: '同期処理をキャンセルしました',
          color: 'green',
          icon: 'lets-icons:check-fill'
        })
      }
    }
  })
}

const onRollbackStaging = async () => {
  confirm.show({
    title: 'ロールバックの確認',
    description: '検証環境を前のバージョンにロールバックしますか？',
    confirmText: 'ロールバック',
    onConfirm: async () => {
      const result = await deploymentsStore.rollbackStagingVersion(
        selectedTenantId.value,
        devEnv.value?.id || ''
      )
      if (result) {
        toast.add({
          title: 'ロールバックが完了しました',
          color: 'green',
          icon: 'lets-icons:check-fill'
        })
        // fetchAllEnvs to refresh the versions
        await environmentsStore.fetchAllEnvs(selectedTenantId.value, true)
      }
    }
  })
}

const isNoDeployment = computed(() => {
  for (const prop in latestDeployment.value) {
    if (Object.hasOwn(latestDeployment.value, prop)) {
      return false
    }
  }
  return true
})
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <UDashboardSection
      data-tour="deployment"
      title="チャットボットのデプロイ"
      description="チャットボットのデプロイを行います。"
    >
      <template #links>
        <UButton
          icon="heroicons:arrow-path"
          label="バージョン情報を更新"
          color="gray"
          variant="ghost"
          size="sm"
          :loading="
            loadings.getLatestDeployment
              || environmentsStore.loadings?.fetchAllEnvs
          "
          @click="() => refreshVersionInfo(true)"
        />
        <UButton
          v-if="
            ([2, 3].includes(latestDeployment?.status)
              || !latestDeployment
              || isNoDeployment)
              && !isDevAndProdHasSameVersion
          "
          type="submit"
          label="実行"
          color="black"
          :disabled="!deployMode.swap && !deployMode.sync"
          @click="onStartDeploy"
        />
      </template>
      <UFormGroup
        name="name"
        label="デプロイモード"
        required
        class="grid grid-cols-1 gap-2 items-center"
        :ui="{ container: '' }"
      >
        <div class="flex flex-row gap-8 mb-6">
          <BaseCheckboxButton
            :model-value="deployMode.swap"
            data-tour="deployment-swap"
            class="flex-1"
            label="スワップ"
            :help="swapHelpTest"
            :disabled="
              [0, 1].includes(latestDeployment?.status)
                || isDevAndProdHasSameVersion
            "
            @update:model-value="onChangeDeployMode('swap')"
          />
          <BaseCheckboxButton
            :model-value="deployMode.sync"
            data-tour="deployment-sync"
            class="flex-1"
            label="同期"
            :help="syncHelpText"
            :disabled="isDevAndProdHasSameVersion || !isDevVersionOlderThanProd"
            :loading="[0, 1].includes(latestDeployment?.status)"
            @update:model-value="onChangeDeployMode('sync')"
          >
            <!-- <template #right>
              <UButton
                v-if="[0].includes(latestDeployment?.status)"
                icon="uiw:stop-o"
                size="xs"
                color="gray"
                variant="solid"
                label="同期をキャンセル"
                :trailing="false"
                :ui="{ rounded: 'rounded-full' }"
                @click="onCancelSync"
              />
            </template> -->
          </BaseCheckboxButton>
        </div>
        <div class="grid grid-cols-2 text-center mb-4 font-bold">
          <div>検証環境</div>
          <div>本番環境</div>
        </div>
        <div
          class="flex flex-row gap-2"
          data-tour="deployment-envs"
        >
          <ULandingCard
            data-tour="deployment-staging"
            class="animate__animated"
            :class="{
              animate__slideInRight: route.query.swap === 'true',
              animate__backInRight: route.query.sync === 'true'
            }"
            :title="devEnv?.id"
            :color="devEnv?.customSettings?.color_primary || 'gray'"
          >
            <template #icon>
              <UAvatar
                :src="devEnv?.customSettings?.avatar_url"
                :ui="{
                  rounded: 'rounded-md'
                }"
              />
            </template>
            <template #description>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                {{ devEnv?.basicSettings?.name }}
              </div>
              <div
                class="flex flex-row gap-2 mt-2 items-center justify-between"
              >
                <UBadge
                  data-tour="staging-version"
                  variant="subtle"
                  :color="devEnv?.customSettings?.color_primary || 'gray'"
                  :ui="{ rounded: 'rounded-full' }"
                >
                  {{ `Version. ${devEnv?.version}` }}
                </UBadge>
                <UButton
                  v-if="isStagingVersionHigher"
                  type="button"
                  label="ロールバック"
                  color="gray"
                  variant="solid"
                  size="xs"
                  icon="heroicons:arrow-uturn-left"
                  :ui="{
                    color: {
                      gray: {
                        solid: 'text-red-500 dark:text-red-500'
                      }
                    }
                  }"
                  @click="onRollbackStaging"
                />
              </div>
            </template>
          </ULandingCard>
          <div class="flex items-center gap-1 flex-col justify-center min-w-20">
            <UIcon
              :name="deployModeIcon"
              class="dark:text-gray-200 text-3xl"
              :class="{
                'rotate-90': [0, 1].includes(latestDeployment?.status)
              }"
            />
            <UProgress
              v-if="[0, 1].includes(latestDeployment?.status)"
              size="xs"
              animation="carousel"
              class="rotate-180"
              :color="prodEnv?.customSettings?.color_primary || 'gray'"
            />
          </div>
          <ULandingCard
            data-tour="deployment-production"
            class="animate__animated"
            :class="{
              animate__slideInLeft: route.query.swap === 'true'
            }"
            :title="prodEnv?.id"
            :color="prodEnv?.customSettings?.color_primary || 'gray'"
          >
            <template #icon>
              <UAvatar
                :src="prodEnv?.customSettings?.avatar_url"
                :ui="{
                  rounded: 'rounded-md'
                }"
              />
            </template>
            <template #description>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                {{ prodEnv?.basicSettings?.name }}
              </div>
              <div class="flex flex-row gap-2 mt-2">
                <UBadge
                  variant="subtle"
                  :color="prodEnv?.customSettings?.color_primary || 'gray'"
                  :ui="{ rounded: 'rounded-full' }"
                >
                  {{ `Version. ${prodEnv?.version}` }}
                </UBadge>
              </div>
            </template>
          </ULandingCard>
        </div>
      </UFormGroup>
    </UDashboardSection>
  </UDashboardPanelContent>
</template>
