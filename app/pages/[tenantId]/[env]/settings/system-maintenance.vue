<script setup lang="ts">
definePageMeta({
  middleware: ['authentication', 'role-guard']
})

const authStore = useAuthStore()
const tenantsStore = useTenantsStore()
const { selectedTenant } = storeToRefs(tenantsStore)

const canManageAll = computed(() => {
  return authStore.isOperator
})
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <!-- Permission Check -->
    <UAlert
      v-if="!canManageAll"
      color="red"
      variant="soft"
      title="アクセス権限がありません"
      description="このページを表示するには管理者以上の権限が必要です。"
      class="mb-6"
    />

    <!-- Content for authorized users -->
    <div
      v-else
      class="space-y-8"
    >
      <div class="space-y-6">
        <MaintenanceSystemControl />
      </div>
    </div>
  </UDashboardPanelContent>
</template>
