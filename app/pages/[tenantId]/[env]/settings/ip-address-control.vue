<script setup lang="ts">
import type {
  IpSetting,
  CreateIpSettingPayload,
  UpdateIpSettingPayload
} from '~/stores/ip-settings'

// Define required permissions for this page
definePageMeta({
  middleware: ['authentication', 'role-guard', 'feature-guard'],
  requiredPermissions: ['view_ip_address_control'],
  requiredFeatures: ['ipAddressControl']
})

const { selectedTenantId } = useApp()
const ipSettingsStore = useIpSettingsStore()
const {
  ipSettings,
  loadings,
  errors,
  ipSettingsPagination,
  ipSettingsTotal,
  ipSettingsFilter
} = storeToRefs(ipSettingsStore)

const toast = useToast()
const confirm = useConfirm()

// Form state
const isFormOpen = ref(false)
const selectedIpSetting = ref<IpSetting | null>(null)
const isUpdateMode = computed(() => !!selectedIpSetting.value)

// Initialize data
onMounted(async () => {
  await ipSettingsStore.fetchIpSettings(selectedTenantId.value)
})
// Reset page
watch(
  () => ipSettingsPagination.value.pageCount,
  () => {
    ipSettingsPagination.value.page = 1
  }
)

// Watch pagination and filters for auto-refresh
watch(
  [ipSettingsPagination, ipSettingsFilter],
  async () => {
    await ipSettingsStore.fetchIpSettings(selectedTenantId.value, true)
  },
  { deep: true }
)

// CRUD operations
const onCreate = () => {
  selectedIpSetting.value = null
  isFormOpen.value = true
}

const onUpdate = (ipSetting: IpSetting) => {
  selectedIpSetting.value = { ...ipSetting }
  isFormOpen.value = true
}

const onDelete = (ipSetting: IpSetting) => {
  confirm.show({
    title: '削除の確認',
    description: `IPアドレス「${ipSetting.ip_address}」の設定を削除しますか？この操作は取り消せません。`,
    confirmText: '削除',
    cancelText: 'キャンセル',
    onConfirm: async () => {
      const result = await ipSettingsStore.deleteIpSetting(
        ipSetting.id,
        selectedTenantId.value
      )
      if (result) {
        toast.add({
          title: '削除完了',
          description: 'IP設定を削除しました。',
          color: 'green'
        })
      } else {
        toast.add({
          id: 'error',
          title: 'エラー',
          description: 'IP設定の削除に失敗しました。',
          color: 'red'
        })
      }
    }
  })
}

const onSubmit = async (
  data: CreateIpSettingPayload | UpdateIpSettingPayload
) => {
  let result

  if (isUpdateMode.value && selectedIpSetting.value) {
    // Update existing IP setting
    result = await ipSettingsStore.updateIpSetting(
      selectedIpSetting.value.id,
      selectedTenantId.value,
      data as UpdateIpSettingPayload
    )
  } else {
    // Create new IP setting
    result = await ipSettingsStore.createIpSetting(
      selectedTenantId.value,
      data as CreateIpSettingPayload
    )
  }

  if (result) {
    toast.add({
      title: isUpdateMode.value ? '更新完了' : '作成完了',
      description: isUpdateMode.value
        ? 'IP設定を更新しました。'
        : 'IP設定を作成しました。',
      color: 'green'
    })
    isFormOpen.value = false
    selectedIpSetting.value = null
  } else {
    toast.add({
      id: 'error',
      title: 'エラー',
      description: isUpdateMode.value
        ? 'IP設定の更新に失敗しました。'
        : 'IP設定の作成に失敗しました。',
      color: 'red'
    })
  }
}

const onRefresh = async () => {
  await ipSettingsStore.fetchIpSettings(selectedTenantId.value, true)
}

const onToggleStatus = async (ipSetting: IpSetting) => {
  const result = await ipSettingsStore.updateIpSetting(
    ipSetting.id,
    selectedTenantId.value,
    { status: !ipSetting.status } as UpdateIpSettingPayload
  )
  if (result) {
    toast.add({
      title: 'ステータス変更',
      description: 'IP設定のステータスを変更しました。',
      color: 'green'
    })
  }
}
</script>

<template>
  <UDashboardPanelContent class="p-0">
    <BaseEmptyList
      v-if="!ipSettings.length && !loadings.fetchIpSettings"
      text="IPアドレス制御の設定がありません"
      init-button
      icon="eos-icons:ip"
      @init="onCreate"
    />
    <IpSettingsTable
      v-else
      :ip-settings="ipSettings"
      :loading="loadings.fetchIpSettings"
      :ip-settings-pagination="ipSettingsPagination"
      :ip-settings-total="ipSettingsTotal"
      :ip-settings-filter="ipSettingsFilter"
      @edit="onUpdate"
      @delete="onDelete"
      @create="onCreate"
      @refresh="onRefresh"
      @toggle-status="onToggleStatus"
    />

    <!-- IP Settings Form Modal -->
    <UDashboardModal
      v-model="isFormOpen"
      :title="selectedIpSetting ? 'IP設定を編集' : '新規IP設定作成'"
      :description="
        selectedIpSetting
          ? 'IP設定の情報を編集します。'
          : '新しいIP設定を作成します。'
      "
      :ui="{ width: 'sm:max-w-md' }"
    >
      <IpSettingsForm
        :loading="loadings.createIpSetting || loadings.updateIpSetting"
        v-bind="selectedIpSetting"
        :is-update-mode="isUpdateMode"
        :error="errors.createIpSetting || errors.updateIpSetting"
        @close="isFormOpen = false"
        @submit="onSubmit"
      />
    </UDashboardModal>
  </UDashboardPanelContent>
</template>
