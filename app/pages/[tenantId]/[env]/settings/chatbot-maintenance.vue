<script setup lang="ts">
definePageMeta({
  middleware: ['authentication', 'role-guard']
})

const authStore = useAuthStore()
const tenantsStore = useTenantsStore()
const { selectedTenant } = storeToRefs(tenantsStore)
// Check user permissions
const canViewMaintenance = computed(() => {
  return authStore.isAdmin || authStore.isOperator
})

const canManageAll = computed(() => {
  return authStore.isOperator
})
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <!-- Permission Check -->
    <UAlert
      v-if="!canViewMaintenance"
      color="red"
      variant="soft"
      title="アクセス権限がありません"
      description="このページを表示するには管理者以上の権限が必要です。"
      class="mb-6"
    />

    <MaintenanceChatbotControl
      v-else
      :tenant="selectedTenant"
      :show-all-controls="false"
    />
  </UDashboardPanelContent>
</template>
