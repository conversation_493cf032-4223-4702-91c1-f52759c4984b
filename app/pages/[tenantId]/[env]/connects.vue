<script setup lang="ts">
const route = useRoute()
const { connectNavigators, basicSettingsNavigators, connectOptionsNavigators }
  = useNavigators()

const breadcrumb = computed(() => {
  const currentSettingNavigator = connectOptionsNavigators.value.find(
    (nav: any) => route.name === nav.id
  )
  return [
    {
      label: connectNavigators.value[0].label,
      icon: connectNavigators.value[0].icon,
      to: `/${route.params.tenantId}/connects`
    },
    {
      label: currentSettingNavigator?.label,
      icon: currentSettingNavigator?.icon
    }
  ]
})
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar>
        <template #title>
          <UBreadcrumb :links="breadcrumb" />
        </template>
      </UDashboardNavbar>

      <UDashboardPanelContent class="flex flex-row p-0 scrollbar-thin">
        <UDashboardPanel
          :width="250"
          :resizable="{ min: 200, max: 400 }"
        >
          <UDashboardSidebar class="pt-4">
            <div
              class="flex flex-col gap-4"
              data-tour="connects-navbars"
            >
              <div>
                <div>
                  <div class="text-[11px] font-bold text-gray-500 pb-2 pl-3">
                    コネクト
                  </div>
                </div>
                <UDashboardSidebarLinks :links="connectOptionsNavigators" />
              </div>
            </div>
          </UDashboardSidebar>
        </UDashboardPanel>

        <NuxtPage />
      </UDashboardPanelContent>
    </UDashboardPanel>
  </UDashboardPage>
</template>
