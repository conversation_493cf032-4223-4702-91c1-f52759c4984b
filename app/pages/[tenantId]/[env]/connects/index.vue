<script setup lang="ts">
const route = useRoute()
const environmentsStore = useEnvironmentsStore()
const { selectedEnv } = storeToRefs(environmentsStore)
const toast = useToast()

// Get tenant ID from route params
const tenantId = route.params.tenantId as string

// Check if current environment is production or staging
const isProd = computed(() => selectedEnv.value?.environment === 1)

// Get the current window host
const currentHost = ref('')

// Set the current host when component is mounted (client-side only)
onMounted(() => {
  currentHost.value = window.location.origin
})

// Generate the appropriate script tag based on environment
const scriptContent = computed(() => {
  // Use current host or fallback to empty string if not available yet
  const host = currentHost.value || ''
  const baseUrl = `${host}/chatbot.min.js`
  const envParam = isProd.value ? '' : '&env=stg'
  return `<script defer="defer" src="${baseUrl}?tenantId=${tenantId}${envParam}"><\/script>`
})

// Function to copy script content to clipboard
const copyToClipboard = () => {
  navigator.clipboard.writeText(scriptContent.value)
    .then(() => {
      toast.add({
        title: 'コピーしました',
        icon: 'i-heroicons-check-circle',
        color: 'green',
        timeout: 2000
      })
    })
    .catch(() => {
      toast.add({
        title: 'コピーに失敗しました',
        icon: 'i-heroicons-exclamation-circle',
        color: 'red',
        timeout: 2000
      })
    })
}
</script>

<template>
  <div
    class="p-4 w-full"
    data-tour="connects-embed"
  >
    <div class="text-sm text-gray-500 dark:text-gray-300 mb-6">
      <h2 class="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-200">
        ウェブサイトにチャットバブルを埋め込み
      </h2>
      <p class="mb-2">
        以下のスクリプトタグをウェブサイトのHTMLに追加することで、チャットボットを埋め込むことができます。
      </p>
      <p class="mb-2">
        現在選択されている環境は <span class="font-semibold">{{ isProd ? '本番' : '検証' }}</span> です。
      </p>
      <p
        v-if="!isProd"
        class="text-amber-600 dark:text-amber-400 mb-2"
      >
        注意: 検証環境のチャットボットを使用するには、ログインが必要です。
      </p>
    </div>

    <div class="mb-6">
      <div class="flex justify-between items-center mb-2">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">
          スクリプトタグ
        </h3>
        <UButton
          icon="i-heroicons-clipboard-document"
          color="gray"
          variant="ghost"
          size="xs"
          @click="copyToClipboard"
        >
          コピー
        </UButton>
      </div>
      <code class="text-xs p-4 dark:bg-gray-800 bg-gray-100 rounded-md block overflow-x-auto">
        {{ scriptContent }}
      </code>
    </div>

    <div class="text-sm text-gray-600 dark:text-gray-400 space-y-2 border-t pt-4 dark:border-gray-700">
      <h3 class="font-medium">
        使用方法:
      </h3>
      <ul class="list-disc pl-5 space-y-1">
        <li>上記のスクリプトタグをウェブサイトの <code class="text-xs bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded">&lt;body&gt;</code> タグの終了直前に配置してください。</li>
        <li>本番環境用のスクリプトは <code class="text-xs bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded">?tenantId=テナント名</code> の形式です。</li>
        <li>検証環境用のスクリプトは <code class="text-xs bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded">?tenantId=テナント名&env=stg</code> の形式で、<code class="text-xs bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded">&env=stg</code> パラメータが追加されています。</li>
      </ul>
    </div>
  </div>
</template>
