<script setup lang="ts">
interface Props {
  endDate?: string // Format: YYYY-MM-DD HH:mm:ss or ISO string (optional for indefinite maintenance)
  startDate?: string // Format: YYYY-MM-DD HH:mm:ss or ISO string (for showing duration)
  message?: string
  showIcon?: boolean
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'warning' | 'danger'
  indefinite?: boolean // Whether this is indefinite maintenance
  isActive?: boolean // Whether maintenance is currently active (between start and end time)
}

const props = withDefaults(defineProps<Props>(), {
  message: 'メンテナンス終了まで',
  showIcon: true,
  size: 'md',
  variant: 'default',
  indefinite: false,
  isActive: true
})

// Check if maintenance should be considered active based on current time
const isCurrentlyActive = computed(() => {
  if (props.indefinite) return true
  if (!props.startDate) return props.isActive

  const now = new Date()
  const start = new Date(props.startDate)

  // If start time has passed, consider it active regardless of isActive prop
  if (now >= start) {
    // If there's an end date, check if we're still within the maintenance window
    if (props.endDate) {
      const end = new Date(props.endDate)
      return now <= end
    }
    // No end date means indefinite maintenance once started
    return true
  }

  return false
})

// Computed message based on mode and state
const displayMessage = computed(() => {
  if (props.indefinite) {
    return props.message || 'メンテナンス継続中'
  }

  // For scheduled maintenance, check if it's currently active or pending
  if (!isCurrentlyActive.value && props.startDate) {
    return props.message || 'メンテナンス開始まで'
  }

  return props.message || 'メンテナンス終了まで'
})

// Reactive countdown state
const timeRemaining = ref({
  days: 0,
  hours: 0,
  minutes: 0,
  seconds: 0,
  total: 0
})

// Duration since start (for indefinite maintenance)
const durationSinceStart = ref({
  days: 0,
  hours: 0,
  minutes: 0,
  seconds: 0,
  total: 0
})

const isExpired = ref(false)
const isIndefinite = computed(() => props.indefinite || !props.endDate)
const intervalId = ref<ReturnType<typeof setInterval> | null>(null)

// Parse end date to ensure it's a valid Date object
const endDateTime = computed(() => {
  if (!props.endDate) return null

  // Handle both YYYY-MM-DD HH:mm:ss and ISO formats
  const dateStr = props.endDate
  if (dateStr.includes('T')) {
    // ISO format
    return new Date(dateStr)
  } else {
    // YYYY-MM-DD HH:mm:ss format
    return new Date(dateStr.replace(' ', 'T'))
  }
})

// Parse start date for duration calculation
const startDateTime = computed(() => {
  if (!props.startDate) return null

  const dateStr = props.startDate
  if (dateStr.includes('T')) {
    return new Date(dateStr)
  } else {
    return new Date(dateStr.replace(' ', 'T'))
  }
})

// Calculate time remaining or duration since start
const calculateTime = () => {
  const now = new Date().getTime()

  if (isIndefinite.value) {
    // Calculate duration since start for indefinite maintenance
    const start = startDateTime.value?.getTime() || now
    const difference = now - start

    durationSinceStart.value = {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
      minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
      seconds: Math.floor((difference % (1000 * 60)) / 1000),
      total: difference
    }
    isExpired.value = false
  } else if (!props.isActive && startDateTime.value) {
    // Calculate countdown to start time for scheduled maintenance that hasn't started yet
    const start = startDateTime.value.getTime()
    const difference = start - now

    if (difference <= 0) {
      // Start time has passed, emit event and continue showing countdown to end
      emit('expired')

      // If there's an end date, show countdown to end
      if (endDateTime.value) {
        const end = endDateTime.value.getTime()
        const endDifference = end - now

        if (endDifference <= 0) {
          timeRemaining.value = {
            days: 0,
            hours: 0,
            minutes: 0,
            seconds: 0,
            total: 0
          }
          isExpired.value = true
          if (intervalId.value) {
            clearInterval(intervalId.value)
            intervalId.value = null
          }
          return
        }

        isExpired.value = false
        timeRemaining.value = {
          days: Math.floor(endDifference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((endDifference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((endDifference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((endDifference % (1000 * 60)) / 1000),
          total: endDifference
        }
      } else {
        // No end date, show duration since start
        const durationDifference = now - start
        durationSinceStart.value = {
          days: Math.floor(durationDifference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((durationDifference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((durationDifference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((durationDifference % (1000 * 60)) / 1000),
          total: durationDifference
        }
        isExpired.value = false
      }
      return
    }

    isExpired.value = false
    timeRemaining.value = {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
      minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
      seconds: Math.floor((difference % (1000 * 60)) / 1000),
      total: difference
    }
  } else {
    // Calculate countdown to end date for active maintenance
    if (!endDateTime.value) {
      // Active maintenance without end date - show duration since start
      const start = startDateTime.value?.getTime() || now
      const difference = now - start

      durationSinceStart.value = {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
        minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((difference % (1000 * 60)) / 1000),
        total: difference
      }
      isExpired.value = false
      return
    }

    const end = endDateTime.value.getTime()
    const difference = end - now

    if (difference <= 0) {
      timeRemaining.value = {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
        total: 0
      }
      isExpired.value = true
      if (intervalId.value) {
        clearInterval(intervalId.value)
        intervalId.value = null
      }
      return
    }

    isExpired.value = false
    timeRemaining.value = {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
      minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
      seconds: Math.floor((difference % (1000 * 60)) / 1000),
      total: difference
    }
  }
}

// Format time unit with leading zero
const formatTimeUnit = (value: number): string => {
  return value.toString().padStart(2, '0')
}

// Get display text for time units
const getTimeText = (_value: number, unit: 'day' | 'hour' | 'minute' | 'second'): string => {
  const units = {
    day: { singular: '日', plural: '日' },
    hour: { singular: '時間', plural: '時間' },
    minute: { singular: '分', plural: '分' },
    second: { singular: '秒', plural: '秒' }
  }
  return units[unit].singular
}

// Computed styles based on props
const containerClasses = computed(() => {
  const base = 'inline-flex items-center gap-2'
  const sizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }
  const variants = {
    default: 'text-gray-700',
    warning: 'text-orange-600',
    danger: 'text-red-600'
  }
  return `${base} ${sizes[props.size]} ${variants[props.variant]}`
})

const timeUnitClasses = computed(() => {
  const base = 'font-mono font-semibold px-2 py-1 rounded'
  const sizes = {
    sm: 'text-xs px-1.5 py-0.5',
    md: 'text-sm px-2 py-1',
    lg: 'text-base px-3 py-1.5'
  }
  const variants = {
    default: 'bg-gray-100 text-gray-800',
    warning: 'bg-orange-100 text-orange-800',
    danger: 'bg-red-100 text-red-800'
  }
  return `${base} ${sizes[props.size]} ${variants[props.variant]}`
})

// Start countdown on mount
onMounted(() => {
  calculateTime()
  intervalId.value = setInterval(calculateTime, 1000)
})

// Clean up interval on unmount
onUnmounted(() => {
  if (intervalId.value) {
    clearInterval(intervalId.value)
  }
})

// Watch for date changes
watch([() => props.endDate, () => props.startDate, () => props.indefinite], () => {
  calculateTime()
}, { immediate: true })

// Emit events
const emit = defineEmits<{
  expired: []
  timeUpdate: [timeRemaining: typeof timeRemaining.value]
}>()

// Watch for expiration
watch(isExpired, (newValue) => {
  if (newValue) {
    emit('expired')
  }
})

// Emit time updates
watch(timeRemaining, (newValue) => {
  emit('timeUpdate', newValue)
}, { deep: true })
</script>

<template>
  <div :class="containerClasses">
    <!-- Icon -->
    <UIcon
      v-if="showIcon"
      name="i-heroicons-clock"
      :class="[
        'flex-shrink-0',
        size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'
      ]"
    />

    <!-- Message -->
    <span class="flex-shrink-0">{{ displayMessage }}</span>

    <!-- Status indicator badges -->
    <UBadge
      v-if="isIndefinite"
      color="orange"
      variant="soft"
      size="xs"
    >
      無期限
    </UBadge>

    <UBadge
      v-else-if="!isCurrentlyActive && startDate"
      color="blue"
      variant="soft"
      size="xs"
    >
      予定
    </UBadge>

    <!-- Countdown or Duration display -->
    <div
      v-if="!isExpired && !isIndefinite"
      class="flex items-center gap-1"
    >
      <!-- Indefinite maintenance - show duration since start -->
      <template v-if="isIndefinite">
        <div class="flex items-center gap-1">
          <!-- Duration display with better formatting -->
          <template v-if="durationSinceStart.days > 0">
            <span :class="timeUnitClasses">
              {{ formatTimeUnit(durationSinceStart.days) }}
            </span>
            <span class="text-xs font-medium">日</span>
          </template>

          <template v-if="durationSinceStart.days > 0 || durationSinceStart.hours > 0">
            <span :class="timeUnitClasses">
              {{ formatTimeUnit(durationSinceStart.hours) }}
            </span>
            <span class="text-xs font-medium">時間</span>
          </template>

          <span :class="timeUnitClasses">
            {{ formatTimeUnit(durationSinceStart.minutes) }}
          </span>
          <span class="text-xs font-medium">分</span>

          <!-- Show seconds only if less than 1 hour -->
          <template v-if="durationSinceStart.days === 0 && durationSinceStart.hours === 0">
            <span :class="timeUnitClasses">
              {{ formatTimeUnit(durationSinceStart.seconds) }}
            </span>
            <span class="text-xs font-medium">秒</span>
          </template>
        </div>

        <!-- Additional info for indefinite maintenance -->
        <div class="flex items-center gap-1 text-xs text-gray-500">
          <UIcon
            name="i-heroicons-information-circle"
            class="w-3 h-3"
          />
          <span>手動終了まで継続</span>
        </div>
      </template>

      <!-- Scheduled maintenance - show countdown to end OR active maintenance without end date -->
      <template v-else>
        <!-- For active maintenance without end date, show duration -->
        <template v-if="isCurrentlyActive && !endDate">
          <div class="flex items-center gap-1">
            <!-- Duration display with better formatting -->
            <template v-if="durationSinceStart.days > 0">
              <span :class="timeUnitClasses">
                {{ formatTimeUnit(durationSinceStart.days) }}
              </span>
              <span class="text-xs font-medium">日</span>
            </template>

            <template v-if="durationSinceStart.days > 0 || durationSinceStart.hours > 0">
              <span :class="timeUnitClasses">
                {{ formatTimeUnit(durationSinceStart.hours) }}
              </span>
              <span class="text-xs font-medium">時間</span>
            </template>

            <span :class="timeUnitClasses">
              {{ formatTimeUnit(durationSinceStart.minutes) }}
            </span>
            <span class="text-xs font-medium">分</span>

            <!-- Show seconds only if less than 1 hour -->
            <template v-if="durationSinceStart.days === 0 && durationSinceStart.hours === 0">
              <span :class="timeUnitClasses">
                {{ formatTimeUnit(durationSinceStart.seconds) }}
              </span>
              <span class="text-xs font-medium">秒</span>
            </template>
          </div>
        </template>

        <!-- For scheduled maintenance or active maintenance with end date, show countdown -->
        <template v-else>
          <!-- Days -->
          <template v-if="timeRemaining.days > 0">
            <span :class="timeUnitClasses">
              {{ formatTimeUnit(timeRemaining.days) }}
            </span>
            <span class="text-xs">{{ getTimeText(timeRemaining.days, 'day') }}</span>
          </template>

          <!-- Hours -->
          <template v-if="timeRemaining.days > 0 || timeRemaining.hours > 0">
            <span :class="timeUnitClasses">
              {{ formatTimeUnit(timeRemaining.hours) }}
            </span>
            <span class="text-xs">{{ getTimeText(timeRemaining.hours, 'hour') }}</span>
          </template>

          <!-- Minutes -->
          <span :class="timeUnitClasses">
            {{ formatTimeUnit(timeRemaining.minutes) }}
          </span>
          <span class="text-xs">{{ getTimeText(timeRemaining.minutes, 'minute') }}</span>

          <!-- Seconds -->
          <span :class="timeUnitClasses">
            {{ formatTimeUnit(timeRemaining.seconds) }}
          </span>
          <span class="text-xs">{{ getTimeText(timeRemaining.seconds, 'second') }}</span>
        </template>
      </template>
    </div>

    <!-- Expired message -->
    <div
      v-else-if="false"
      class="flex items-center gap-2"
    >
      <UIcon
        name="i-heroicons-check-circle"
        :class="[
          'text-green-500',
          size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'
        ]"
      />
      <span class="text-green-600 font-medium">
        {{ isIndefinite ? 'メンテナンス継続中' : (!isActive && startDate ? 'メンテナンス開始' : 'メンテナンス終了') }}
      </span>
    </div>
  </div>
</template>
