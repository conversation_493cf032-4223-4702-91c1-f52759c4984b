<script setup lang="ts">
const { hasPermission } = useAppPermissions()

const props = defineProps({
  users: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  lockedUsers: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  usersPagination: {
    type: Object,
    required: true
  },
  usersTotal: {
    type: Number,
    required: true
  },
  usersFilter: {
    type: Object,
    required: true
  },
  selectedUsers: {
    type: Array as PropType<any[]>,
    default: () => []
  }
})
const search = ref('')

const emit = defineEmits([
  'edit',
  'delete',
  'create',
  'refresh',
  'deleteMany',
  'update:selectedUsers',
  'view-logs',
  'releaseUserLock',
  'toggleUserStatus'
])
enum userRole {
  STAFF = 'staff',
  ADMIN = 'admin',
  PNL_ADMIN = 'operator'
}
const defaultUserTypeuses = [
  { label: 'スタッフ', value: userRole.STAFF },
  { label: '管理者', value: userRole.ADMIN },
  { label: 'PNL管理者', value: userRole.PNL_ADMIN }
]

const defaultStatuses = [
  { label: '有効', value: 'enabled' },
  { label: '無効', value: 'disabled' }
]
function getItems(user: any) {
  const items = [[]] as any

  // Only add edit option if user has permission
  if (hasPermission(PERMISSIONS.EDIT_USER)) {
    items[0].push({
      label: 'ユーザ編集',
      click: () => emit('edit', user)
    })
  }

  if (
    hasPermission(PERMISSIONS.RELEASE_USER_LOCK)
    && props.lockedUsers.includes(user.username)
  ) {
    items[0].push({
      label: 'ユーザロック解除',
      click: () => emit('releaseUserLock', user.username)
    })
  }

  // Add enable/disable option if user has permission
  if (hasPermission(PERMISSIONS.EDIT_USER)) {
    items[0].push({
      label: user.enabled ? '無効化' : '有効化',
      click: () => emit('toggleUserStatus', user)
    })
  }

  // Only add delete option if user has permission
  if (hasPermission(PERMISSIONS.DELETE_USER)) {
    items[0].push({
      label: 'ユーザ削除',
      labelClass: 'text-red-500 dark:text-red-400',
      click: () => emit('delete', user)
    })
  }
  return items
}

const defaultColumns = [
  {
    key: 'display_name',
    label: '名前',
    sortable: true
  },
  {
    key: 'username',
    label: 'アカウント情報',
    sortable: true
  },
  {
    key: 'user_type',
    label: '権限',
    sortable: true
  },
  // {
  //   key: 'created_at',
  //   label: '作成日時',
  //   sortable: true
  // },
  // {
  //   key: 'updated_at',
  //   label: '更新日時',
  //   sortable: true
  // },
  {
    key: 'enabled',
    label: 'ステータス',
    sortable: false,
    class: 'text-center min-w-36'
  }
]
const items = [
  [
    {
      label: '一括削除',
      icon: 'carbon:batch-job',
      click: () => {
        emit('deleteMany')
      }
    }
  ]
]
</script>

<template>
  <UCard
    class="w-full"
    :ui="{
      base: '',
      ring: '',
      divide: 'divide-y divide-gray-200 dark:divide-gray-700',
      header: { padding: '!px-3 !py-3' },
      body: {
        padding: '',
        base: 'divide-y divide-gray-200 dark:divide-gray-700'
      },
      footer: { padding: 'p-4' }
    }"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <h2
          class="flex items-center gap-2 font-semibold text-md text-gray-900 dark:text-white leading-tight"
        >
          ユーザ一覧
          <UBadge
            v-if="usersTotal"
            :label="usersTotal"
          />
        </h2>
        <!-- Use slot for actions to allow parent component to control permissions -->
        <slot name="actions">
          <UButton
            label="ユーザ追加"
            icon="icomoon-free:user-plus"
            color="gray"
            size="sm"
            @click="emit('create')"
          />
        </slot>
      </div>
    </template>

    <!-- Filters -->
    <div
      class="flex items-center justify-between gap-3 px-4 py-3"
      data-tour="user-filter"
    >
      <div class="flex gap-5 items-center">
        <UInput
          v-model="props.usersFilter.username"
          icon="i-heroicons-magnifying-glass-20-solid"
          autocomplete="off"
          placeholder="ユーザ名の検索"
          @keydown.esc="$event.target.blur()"
        >
          <template #trailing>
            <UKbd value="/" />
          </template>
        </UInput>

        <USelectMenu
          v-model="props.usersFilter.status"
          icon="i-heroicons-check-circle"
          placeholder="権限"
          class="w-40"
          :options="defaultUserTypeuses"
          :ui-menu="{ option: { base: 'capitalize' } }"
        >
          <template
            #label
            #trailing{{
            option
            }}
          >
            <div v-if="props.usersFilter.status">
              {{ props.usersFilter.status.label }}
            </div>
            <div v-else>
              権限
            </div>
          </template>
          <template
            v-if="props.usersFilter.status"
            #trailing
          >
            <UButton
              v-if="defaultUserTypeuses"
              size="xs"
              icon="i-lucide-delete"
              color="gray"
              :class="[
                'ml-2 px-2 py-1 rounded  hover:text-red-600 !pointer-events-auto'
              ]"
              @click.stop="
                () => {
                  props.usersFilter.status = null;
                }
              "
            />
          </template>
        </USelectMenu>

        <USelectMenu
          v-model="props.usersFilter.enabled"
          icon="i-heroicons-check-circle"
          placeholder="ステータス"
          class="w-36"
          multiple
          :options="defaultStatuses"
          :ui-menu="{ option: { base: 'capitalize' } }"
        >
          <template #label>
            <div v-if="props.usersFilter.enabled?.length">
              {{
                props.usersFilter.enabled
                  ?.map((obj: any) => obj.label)
                  .join(", ")
              }}
            </div>
            <div v-else>
              ステータス
            </div>
          </template>
        </USelectMenu>
      </div>
      <div class="flex items-center gap-1.5">
        <div
          v-if="selectedUsers.length"
          data-tour="selected-user-menu"
        >
          <UDropdown
            :items="items"
            :popper="{ placement: 'bottom-start' }"
          >
            <UButton
              color="white"
              :label="`一括操作（${selectedUsers.length}件）`"
              icon="fluent:form-multiple-20-regular"
              trailing-icon="i-heroicons-chevron-down-20-solid"
              size="sm"
            />
          </UDropdown>
        </div>
        <UButton
          data-tour="user-refresh"
          icon="prime:sync"
          color="gray"
          size="sm"
          @click="emit('refresh')"
        />
      </div>
    </div>
    <UTable
      v-if="users"
      :rows="users"
      :columns="defaultColumns"
      :model-value="selectedUsers"
      class="w-full"
      :ui="{
        divide: 'divide-gray-200 dark:divide-gray-800',
        tr: { base: 'group' }
      }"
      :loading="loading"
      @update:model-value="$emit('update:selectedUsers', $event)"
    >
      <template #created_at-data="{ row }">
        <div>
          <div class="text-xs">
            {{ formatDateTime(row.created_at) }}
          </div>
          <div class="text-gray-500 dark:text-gray-500 text-xs">
            作成者: {{ row.created_username }} ({{
              formatDistanceStrictDateTime(row.created_at)
            }})
          </div>
        </div>
      </template>

      <template #updated_at-data="{ row }">
        <div
          v-if="row.updated_at === row.created_at"
          class="text-gray-500 dark:text-gray-500"
        >
          --
        </div>
        <div v-else>
          <div>
            {{ formatDateTime(row.updated_at) }}
          </div>
          <div class="text-gray-500 dark:text-gray-500 text-xs">
            更新者: {{ row. updated_username }} ({{
              formatDistanceStrictDateTime(row.updated_at)
            }})
          </div>
        </div>
      </template>
      <template #lock_status-data="{ row }">
        <UBadge
          :label="
            !lockedUsers.includes(row.username) ? 'ロックなし' : 'ロック中'
          "
          :color="!lockedUsers.includes(row.username) ? 'green' : 'red'"
          :icon="
            !lockedUsers.includes(row.username)
              ? 'heroicons:lock-open'
              : 'heroicons:lock-closed'
          "
          variant="subtle"
          size="sm"
          :ui="{
            rounded: 'rounded-full'
          }"
        />
      </template>

      <template #logs-data="{ row }">
        <div class="flex flex-row items-center justify-center">
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-archive-box"
            size="xs"
            @click="emit('view-logs', row)"
          />
        </div>
      </template>
      <template #username-data="{ row }">
        <div class="flex items-center gap-2">
          <UTooltip
            v-if="lockedUsers.includes(row.username)"
            text="このユーザはロックされています。"
            @click="emit('releaseUserLock', row.username)"
          >
            <UAvatar
              icon="heroicons:lock-closed"
              size="md"
              :ui="{
                icon: {
                  base: 'text-red-500 dark:text-red-400',
                  size: {
                    md: 'w-6 h-6'
                  }
                },
                rounded: 'rounded-lg'
              }"
              class="cursor-pointer"
            />
          </UTooltip>
          <UTooltip
            text="クリックしてアクセスログを表示"
            @click="emit('view-logs', row)"
          >
            <div class="flex flex-col hover:underline cursor-pointer">
              <div class="text-sm font-medium text-gray-900 dark:text-white">
                {{ row.username }}
              </div>
              <div class="text-gray-500 dark:text-gray-400 text-xs">
                {{ row.email }}
              </div>
            </div>
          </UTooltip>
        </div>
      </template>

      <template #display_name-data="{ row }">
        <div class="flex items-center gap-1">
          <UAvatar
            v-if="row.display_name"
            size="xs"
            :alt="row?.display_name"
          />
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ row.display_name }}
          </div>
        </div>
      </template>

      <template #user_type-data="{ row }">
        <UBadge
          size="sm"
          variant="subtle"
          :trailing="false"
          v-bind="userTypeObject(row.user_type)"
        />
      </template>

      <template #enabled-data="{ row }">
        <div class="flex items-center gap-3 justify-between">
          <BaseStatusToggleNoConfirm
            v-model="row.enabled"
            class="capitalize flex-1 justify-center max-w-16"
            @toggle="emit('toggleUserStatus', row)"
          />

          <UDropdown
            class="group-hover:block"
            :class="{
              block: loading,
              hidden: !loading
            }"
            :items="getItems(row)"
            :popper="{ placement: 'bottom-start' }"
          >
            <UButton
              class="row-menu"
              color="white"
              icon="charm:menu-meatball"
              size="xs"
              square
              :loading="loading"
            />
          </UDropdown>
        </div>
      </template>
    </UTable>

    <!-- Number of rows & Pagination -->
    <template #footer>
      <div class="flex flex-wrap justify-between items-center">
        <div class="flex items-center gap-1.5">
          <span class="text-sm leading-5">表示件数:</span>

          <USelect
            v-model="usersPagination.pageCount"
            data-tour="user-pagecount"
            :options="[3, 5, 10, 20, 30, 40]"
            class="w-20"
          />
        </div>

        <UPagination
          v-model="usersPagination.page"
          data-tour="user-pagination"
          :page-count="usersPagination.pageCount"
          :total="usersTotal"
          :ui="{
            wrapper: 'flex items-center gap-1',
            rounded: '!rounded-full min-w-[32px] justify-center',
            default: {
              activeButton: {
                variant: 'outline'
              }
            }
          }"
        />
      </div>
    </template>
  </UCard>
</template>
